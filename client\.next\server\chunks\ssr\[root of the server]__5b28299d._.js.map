{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  return localStorage.getItem('studentToken');\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,OAAO,aAAa,OAAO,CAAC;AAC9B;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div data-slot=\"form-item\" className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAyB,CAAC;AAErE,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAwB,CAAC;AAEnE,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,aAAU;YAAY,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAa,GAAG,KAAK;;;;;;;;;;;AAGlF;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAyD;IAC1F,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBAAkB,CAAC,QAAQ,GAAG,mBAAmB,GAAG,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAC3F,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',\r\n        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport {\r\n  format,\r\n  startOfMonth,\r\n  endOfMonth,\r\n  startOfWeek,\r\n  endOfWeek,\r\n  addDays,\r\n  addMonths,\r\n  subMonths,\r\n  isSameMonth,\r\n  isSameDay,\r\n  isToday\r\n} from 'date-fns';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface CalendarProps {\r\n  className?: string;\r\n  selected?: Date;\r\n  onSelect?: (date: Date) => void;\r\n  disabled?: (date: Date) => boolean;\r\n  mode?: 'single' | 'range';\r\n  month?: Date;\r\n  onMonthChange?: (date: Date) => void;\r\n  fromYear?: number;\r\n  toYear?: number;\r\n  captionLayout?: 'buttons' | 'dropdown';\r\n  initialFocus?: boolean;\r\n  classNames?: Record<string, string>;\r\n}\r\n\r\nfunction Calendar({\r\n  className,\r\n  selected,\r\n  onSelect,\r\n  disabled,\r\n  month,\r\n  onMonthChange,\r\n  fromYear,\r\n  toYear,\r\n  captionLayout = 'buttons',\r\n  classNames,\r\n  ...props\r\n}: CalendarProps) {\r\n  const [currentMonth, setCurrentMonth] = React.useState(month || selected || new Date());\r\n\r\n  React.useEffect(() => {\r\n    if (month) {\r\n      setCurrentMonth(month);\r\n    }\r\n  }, [month]);\r\n\r\n  const monthStart = startOfMonth(currentMonth);\r\n  const monthEnd = endOfMonth(monthStart);\r\n  const startDate = startOfWeek(monthStart);\r\n  const endDate = endOfWeek(monthEnd);\r\n\r\n  const dateFormat = 'MMMM yyyy';\r\n  const rows = [];\r\n  let days = [];\r\n  let day = startDate;\r\n  let formattedDate = '';\r\n\r\n  // Generate calendar days\r\n  while (day <= endDate) {\r\n    for (let i = 0; i < 7; i++) {\r\n      formattedDate = format(day, 'd');\r\n      const cloneDay = day;\r\n\r\n      days.push(\r\n        <div\r\n          key={day.toString()}\r\n          className={cn(\r\n            'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer',\r\n            'h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground',\r\n            {\r\n              'text-muted-foreground': !isSameMonth(day, monthStart),\r\n              'bg-primary text-primary-foreground': selected && isSameDay(day, selected),\r\n              'bg-accent text-accent-foreground': isToday(day) && (!selected || !isSameDay(day, selected)),\r\n              'opacity-50 cursor-not-allowed': disabled && disabled(day),\r\n            }\r\n          )}\r\n          onClick={() => {\r\n            if (!disabled || !disabled(cloneDay)) {\r\n              onSelect?.(cloneDay);\r\n            }\r\n          }}\r\n        >\r\n          <span className=\"font-normal\">{formattedDate}</span>\r\n        </div>\r\n      );\r\n      day = addDays(day, 1);\r\n    }\r\n    rows.push(\r\n      <div className=\"flex w-full mt-2\" key={day.toString()}>\r\n        {days}\r\n      </div>\r\n    );\r\n    days = [];\r\n  }\r\n\r\n  const nextMonth = () => {\r\n    const newMonth = addMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const prevMonth = () => {\r\n    const newMonth = subMonths(currentMonth, 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(currentMonth.getFullYear(), parseInt(e.target.value), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newMonth = new Date(parseInt(e.target.value), currentMonth.getMonth(), 1);\r\n    setCurrentMonth(newMonth);\r\n    onMonthChange?.(newMonth);\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-3', className)} {...props}>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Header */}\r\n        <div className={cn('flex justify-center pt-1 relative items-center w-full', classNames?.caption)}>\r\n          {captionLayout === 'dropdown' ? (\r\n            <div className=\"flex gap-2\">\r\n              <select\r\n                value={currentMonth.getMonth()}\r\n                onChange={handleMonthChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: 12 }, (_, i) => (\r\n                  <option key={i} value={i}>\r\n                    {format(new Date(2000, i, 1), 'MMMM')}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <select\r\n                value={currentMonth.getFullYear()}\r\n                onChange={handleYearChange}\r\n                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}\r\n              >\r\n                {Array.from({ length: (toYear || new Date().getFullYear()) - (fromYear || 1950) + 1 }, (_, i) => {\r\n                  const year = (fromYear || 1950) + i;\r\n                  return (\r\n                    <option key={year} value={year}>\r\n                      {year}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={prevMonth}\r\n              >\r\n                <ChevronLeft className=\"size-4\" />\r\n              </Button>\r\n              <div className={cn('text-sm font-medium', classNames?.caption_label)}>\r\n                {format(currentMonth, dateFormat)}\r\n              </div>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n                onClick={nextMonth}\r\n              >\r\n                <ChevronRight className=\"size-4\" />\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        {/* Calendar Grid */}\r\n        <div className=\"w-full border-collapse space-x-1\">\r\n          {/* Days of week header */}\r\n          <div className=\"flex\">\r\n            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (\r\n              <div\r\n                key={day}\r\n                className=\"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center\"\r\n              >\r\n                {day}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Calendar rows */}\r\n          {rows}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAnBA;;;;;;;AAoCA,SAAS,SAAS,EAChB,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,aAAa,EACb,QAAQ,EACR,MAAM,EACN,gBAAgB,SAAS,EACzB,UAAU,EACV,GAAG,OACW;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,SAAS,YAAY,IAAI;IAEhF,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,OAAO;YACT,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE;IAE1B,MAAM,aAAa;IACnB,MAAM,OAAO,EAAE;IACf,IAAI,OAAO,EAAE;IACb,IAAI,MAAM;IACV,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,MAAO,OAAO,QAAS;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,gBAAgB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;YAC5B,MAAM,WAAW;YAEjB,KAAK,IAAI,eACP,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,oGACA;oBACE,yBAAyB,CAAC,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,KAAK;oBAC3C,sCAAsC,YAAY,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK;oBACjE,oCAAoC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;oBAC3F,iCAAiC,YAAY,SAAS;gBACxD;gBAEF,SAAS;oBACP,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW;wBACpC,WAAW;oBACb;gBACF;0BAEA,cAAA,8OAAC;oBAAK,WAAU;8BAAe;;;;;;eAjB1B,IAAI,QAAQ;;;;;YAoBrB,MAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,KAAK;QACrB;QACA,KAAK,IAAI,eACP,8OAAC;YAAI,WAAU;sBACZ;WADoC,IAAI,QAAQ;;;;;QAIrD,OAAO,EAAE;IACX;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,YAAY;QAChB,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACzC,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,IAAI,KAAK,aAAa,WAAW,IAAI,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG;QAChF,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,IAAI,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK,GAAG,aAAa,QAAQ,IAAI;QAC7E,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QAAa,GAAG,KAAK;kBAC7C,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD,YAAY;8BACrF,kBAAkB,2BACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAO,aAAa,QAAQ;gCAC5B,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC;wCAAe,OAAO;kDACpB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,GAAG,IAAI;uCADnB;;;;;;;;;;0CAKjB,8OAAC;gCACC,OAAO,aAAa,WAAW;gCAC/B,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sIAAsI,YAAY;0CAE/J,MAAM,IAAI,CAAC;oCAAE,QAAQ,CAAC,UAAU,IAAI,OAAO,WAAW,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI;gCAAE,GAAG,CAAC,GAAG;oCACzF,MAAM,OAAO,CAAC,YAAY,IAAI,IAAI;oCAClC,qBACE,8OAAC;wCAAkB,OAAO;kDACvB;uCADU;;;;;gCAIjB;;;;;;;;;;;6CAIJ;;0CACE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,YAAY;0CACnD,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;0CAExB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;6BAAK,CAAC,GAAG,CAAC,CAAC,oBAC/C,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;wBASV;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAyBF;uCAEe", "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAoCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { io } from 'socket.io-client';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n  userId?: string;\r\n}\r\n\r\nexport default function NotificationBell({ userType, userId }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    // Set up Socket.io connection for real-time notifications\r\n    if (userId) {\r\n      const socketConnection = io(\"https://www.uest.in\", {\r\n        withCredentials: true,\r\n        path: '/uapi/socket.io',\r\n      });\r\n\r\n      socketConnection.on('connect', () => {\r\n        // Join notification room\r\n        socketConnection.emit('join', { username: userId, userType, userId });\r\n      });\r\n\r\n      // Listen for new notifications\r\n      socketConnection.on('newNotification', (notification: Notification) => {\r\n        setNotifications(prev => [notification, ...prev]);\r\n        toast.info(notification.title);\r\n      });\r\n\r\n      // Listen for notification updates (for chat message count updates)\r\n      socketConnection.on('notificationUpdated', (updatedNotification: Notification) => {\r\n        setNotifications(prev =>\r\n          prev.map(notif =>\r\n            notif.id === updatedNotification.id ? updatedNotification : notif\r\n          )\r\n        );\r\n        toast.info(updatedNotification.title);\r\n      });\r\n\r\n      // Listen for notification count updates\r\n      socketConnection.on('notificationCountUpdate', (data: { count: number }) => {\r\n        setUnreadCount(data.count);\r\n      });\r\n\r\n\r\n\r\n      return () => {\r\n        socketConnection.disconnect();\r\n      };\r\n    } else {\r\n      // Fallback to polling if no userId\r\n      const interval = setInterval(fetchNotifications, 30000);\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [userType, userId, fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"relative group rounded-full border-2 border-orange-500 hover:border-orange-400 bg-black hover:bg-gray-900 transition-all duration-200 h-8 w-8 md:h-10 md:w-10\"\r\n        >\r\n          <div className=\"absolute rounded-full inset-0 bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\r\n          <div className=\"relative z-10 flex items-center justify-center\">\r\n            <Bell className=\"h-4 w-4 md:h-5 md:w-5 text-orange-500 group-hover:text-orange-400 transition-colors duration-200\" />\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1 -right-1 md:-top-2 md:-right-2 h-4 w-4 md:h-5 md:w-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-bold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n        <div className=\"p-4 border-b\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h3 className=\"font-semibold\">Notifications</h3>\r\n            <div className=\"flex gap-2\">\r\n              {unreadCount > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleMarkAllAsRead}\r\n                  className=\"text-xs\"\r\n                >\r\n                  Mark all read\r\n                </Button>\r\n              )}\r\n              {notifications.length > 0 && unreadCount === 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleRemoveAllClick}\r\n                  className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  Remove all\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"h-80 overflow-y-auto\">\r\n          {loading ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              Loading notifications...\r\n            </div>\r\n          ) : notifications.length === 0 ? (\r\n            <div className=\"p-4 text-center text-muted-foreground\">\r\n              No notifications yet\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y\">\r\n              {Array.isArray(notifications) && notifications.map((notification) => (\r\n                <div\r\n                  key={notification.id}\r\n                  className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${\r\n                    !notification.isRead ? 'bg-blue-50/50' : ''\r\n                  }`}\r\n                  onClick={() => handleNotificationClick(notification)}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className={`w-2 h-2 rounded-full mt-2 ${\r\n                      !notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                    }`} />\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                      <p className=\"text-sm text-muted-foreground mt-1\">\r\n                        {notification.message}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground mt-2\">\r\n                        {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n        {safeNotifications.length > 0 && (\r\n          <div className=\"p-3 border-t bg-muted/30\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs\"\r\n              onClick={() => {\r\n                setIsOpen(false);\r\n                router.push('/notifications');\r\n              }}\r\n            >\r\n              View All Notifications\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n\r\n    {/* Delete Confirmation Dialog */}\r\n    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            Are you sure you want to remove all notifications? This action cannot be undone.\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n          <AlertDialogAction\r\n            onClick={handleConfirmRemoveAll}\r\n            className=\"bg-red-600 hover:bg-red-700\"\r\n          >\r\n            Remove All\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;AAAA;AACA;AArCA;;;;;;;;;;;;AA4Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAE,MAAM,EAAyB;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,IAAI;YACJ,IAAI;YAEJ,IAAI,aAAa,SAAS;gBACxB,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBACxC,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD;YAClC,OAAO;gBACL,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;gBAC1C,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;YACpC;YAEA,2CAA2C;YAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;YACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YACpD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,iBAAiB,EAAE;YACnB,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,0DAA0D;QAC1D,IAAI,QAAQ;YACV,MAAM,mBAAmB,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;gBACjD,iBAAiB;gBACjB,MAAM;YACR;YAEA,iBAAiB,EAAE,CAAC,WAAW;gBAC7B,yBAAyB;gBACzB,iBAAiB,IAAI,CAAC,QAAQ;oBAAE,UAAU;oBAAQ;oBAAU;gBAAO;YACrE;YAEA,+BAA+B;YAC/B,iBAAiB,EAAE,CAAC,mBAAmB,CAAC;gBACtC,iBAAiB,CAAA,OAAQ;wBAAC;2BAAiB;qBAAK;gBAChD,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,aAAa,KAAK;YAC/B;YAEA,mEAAmE;YACnE,iBAAiB,EAAE,CAAC,uBAAuB,CAAC;gBAC1C,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,oBAAoB,EAAE,GAAG,sBAAsB;gBAGhE,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,oBAAoB,KAAK;YACtC;YAEA,wCAAwC;YACxC,iBAAiB,EAAE,CAAC,2BAA2B,CAAC;gBAC9C,eAAe,KAAK,KAAK;YAC3B;YAIA,OAAO;gBACL,iBAAiB,UAAU;YAC7B;QACF,OAAO;YACL,mCAAmC;YACnC,MAAM,WAAW,YAAY,oBAAoB;YACjD,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAU;QAAQ;KAAmB;IAEzC,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACrC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOxC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,8OAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,8OAAC;4CAEC,WAAW,CAAC,uDAAuD,EACjE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IACzC;4CACF,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,0BAA0B,EACzC,CAAC,aAAa,MAAM,GAAG,gBAAgB,eACvC;;;;;;kEACF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAhB1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAyB7B,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQX", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1823, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  localStorage.removeItem('studentToken');\r\n  localStorage.removeItem('student_data');\r\n  return {\r\n    success: true,\r\n    message: 'Logged out successfully',\r\n  };\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;IACxB,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1949, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,CAAC,WAAW;gBACd,UAAU;gBACV;YACF;YACA,MAAM,WAAmC,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;YACpC,OAAO;gBACL,UAAU;YACZ;QACF;QACA;IACF,GAAG;QAAC;KAAU;IAEd,qBACK,8OAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 2044, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, ChevronRight, LayoutDashboard, BadgeCent, MessageSquare, GraduationCap, Flame } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport {\r\n  Avatar,\r\n  AvatarFallback,\r\n} from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { motion, useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\n\r\n\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const [isHovering, setIsHovering] = useState(false);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n \r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem('student_data');\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem('student_data');\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((time, delta) => {\r\n    if (isHovering || contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event('storage'));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem('student_data');\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem('student_data');\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\", icon: <Flame className=\"w-4 h-4\" />, isNew: true },\r\n    {\r\n      href: \"/mock-exam-card\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <span>Daily Quiz</span>\r\n          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}\r\n        </span>\r\n      ),\r\n      icon: <BadgeCent className=\"w-4 h-4\" />,\r\n    },\r\n   \r\n    { href: \"/careers\", label: \"Career\", icon: <Briefcase className=\"w-4 h-4\" /> },\r\n  ];\r\n\r\n  const bannerContent = (\r\n    <div className=\"inline-flex items-center space-x-4 whitespace-nowrap\">\r\n      <span className=\"text-sm md:text-xl font-semibold text-black\">\r\n        U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion\r\n      </span>\r\n      <button\r\n        className=\"inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition\"\r\n        style={{ border: '2px solid black' }}\r\n        onClick={() => router.push(`/uwhiz-info/${1}`)}\r\n      >\r\n        Apply Now <ChevronRight className=\"ml-2 h-4 w-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black overflow-x-hidden\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-20 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={150}\r\n                height={50}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-4\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.icon}\r\n                  {link.label}\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* Mobile Notification Bell */}\r\n            <div className=\"flex md:hidden items-center space-x-2\">\r\n              {isAuthenticated && (\r\n                <NotificationBell userType=\"class\" userId={user?.id} />\r\n              )}\r\n              {isStudentLoggedIn && (\r\n                <NotificationBell userType=\"student\" userId={studentData?.id} />\r\n              )}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\r\n              </Button>\r\n            </div>\r\n\r\n            <div className=\"hidden md:flex items-center space-x-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <NotificationBell userType=\"class\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full border-2 border-orange-500 group bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              <div className=\"h-8 border-l border-orange-500/20\" />\r\n\r\n              {isAuthenticated && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                            : \"CT\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {user?.firstName && user?.lastName\r\n                            ? `${user.firstName} ${user.lastName}`\r\n                            : user?.className || \"Class Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{user?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/profile\" className=\"flex items-center\">\r\n                          <User className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button onClick={() => accessClassDashboard()} className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <User className=\"mr-2 h-4 w-4\" />\r\n                        <span>My Dashboard</span>\r\n                      </Button>\r\n                      {/* <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/question-bank\" className=\"flex items-center\">\r\n                          <FileQuestion className=\"mr-2 h-4 w-4\" />\r\n                          <span>Question Bank</span>\r\n                        </Link>\r\n                      </Button> */}\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/classes/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={async () => {\r\n                          try {\r\n                            const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                            if (response.data.success) {\r\n                              router.push(\"/\");\r\n                              dispatch(clearUser());\r\n                              localStorage.removeItem(\"token\");\r\n                              toast.success(\"Logged out successfully\");\r\n                            }\r\n                          } catch (error) {\r\n                            console.error(\"Logout error:\", error);\r\n                            toast.error(\"Failed to logout\");\r\n                          }\r\n                        }}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  className=\"bg-customOrange hover:bg-[#E88143] text-white mr-4\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/class/login\">Join as a Tutor/Class</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white\"\r\n                  asChild\r\n                >\r\n                  <Link href=\"/student/login\">Student Login</Link>\r\n                </Button>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  <NotificationBell userType=\"student\" />\r\n                  <>\r\n                  <Link href=\"/coins\" passHref>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"relative rounded-full group bg-black h-10 w-10 border-2 border-orange-500\"\r\n                      >\r\n                        <div className=\"absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                        <div className=\"relative z-10 \">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={32}\r\n                            height={32}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                      </Button>\r\n                    </Link>\r\n                </>\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className=\"relative rounded-full group border-2 border-orange-500 bg-black text-white hover:text-orange-400 h-10 w-10\"\r\n                    >\r\n                      <MessageSquare className=\"h-5 w-5\" />\r\n                    </Button>\r\n                  </Link>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <Popover>\r\n                  <PopoverTrigger asChild>\r\n                    <Avatar className=\"cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors h-10 w-10\">\r\n                      <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                        {studentData?.firstName && studentData?.lastName\r\n                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                          : \"ST\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                  </PopoverTrigger>\r\n                  <PopoverContent className=\"w-64 bg-white\">\r\n                    <div className=\"flex items-center gap-3 mb-3 pb-2 border-b\">\r\n                      <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                        <AvatarFallback className=\"bg-white text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium text-black\">\r\n                          {studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName} ${studentData.lastName}`\r\n                            : \"Student Account\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-600\">{studentData?.contactNo || \"<EMAIL>\"}</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-2\">\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/profile\" className=\"flex items-center\">\r\n                          <UserCircle className=\"mr-2 h-4 w-4\" />\r\n                          <span>Profile</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/wishlist\" className=\"flex items-center\">\r\n                          <ShoppingBag className=\"mr-2 h-4 w-4\" />\r\n                          <span>My Wishlist</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button asChild className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\">\r\n                        <Link href=\"/student/referral-dashboard\" className=\"flex items-center\">\r\n                          <Share2 className=\"mr-2 h-4 w-4\" />\r\n                          <span>Referral Dashboard</span>\r\n                        </Link>\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                        onClick={handleStudentLogout}\r\n                      >\r\n                        Logout\r\n                      </Button>\r\n                    </div>\r\n                  </PopoverContent>\r\n                </Popover>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"w-screen bg-[#FD904B] border-y border-black relative mt-1\">\r\n          <div className=\"absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0\"></div>\r\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden\">\r\n            <motion.div\r\n              className=\"inline-flex py-2 px-4\"\r\n              style={{ x }}\r\n              onMouseEnter={() => setIsHovering(true)}\r\n              onMouseLeave={() => setIsHovering(false)}\r\n            >\r\n              <div ref={contentRef} className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                {bannerContent}\r\n              </div>\r\n              <div className=\"inline-flex items-center space-x-4 whitespace-nowrap pr-8\">\r\n                {bannerContent}\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${\r\n            isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex justify-end\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                <X className=\"h-6 w-6\" />\r\n              </Button>\r\n            </div>\r\n\r\n            <nav className=\"flex flex-col space-y-2 mt-8\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {link.icon}\r\n                    {typeof link.label === \"string\" ? (\r\n                      <span>{link.label}</span>\r\n                    ) : (\r\n                      link.label\r\n                    )}\r\n                  </div>\r\n                  {link.label === \"Find School\" && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse\">\r\n                      Coming Soon\r\n                    </span>\r\n                  )}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      New\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-4\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  <Link href=\"/classes/profile\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <User className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Profile</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                    onClick={() => accessClassDashboard()}\r\n                  >\r\n                    <div className=\"absolute inset-0\" />\r\n                    <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                      <div className=\"p-1.5 rounded-full\">\r\n                        <LayoutDashboard className=\"h-5 w-5 text-white\" />\r\n                      </div>\r\n                      <span className=\"font-medium text-gray-300\">My Dashboard</span>\r\n                    </div>\r\n                  </Button>\r\n                  {/* <Link href=\"/classes/question-bank\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <FileQuestion className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Question Bank</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link> */}\r\n                  <Link href=\"/classes/referral-dashboard\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Share2 className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Referral Dashboard</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/classes/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-white bg-black hover:bg-[#ff914d]/90\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0\" />\r\n                      <div className=\"relative z-10 flex items-center gap-3 py-2\">\r\n                        <div className=\"p-1.5 rounded-full\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 hover:text-white mt-3\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\"/auth-client/logout\", {});\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {studentData?.firstName && studentData?.lastName && (\r\n                    <div className=\"p-3 border border-[#ff914d]/20 rounded-lg bg-white\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-12 w-12 border-2 border-[#ff914d]\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">{`${studentData.firstName} ${studentData.lastName}`}</p>\r\n                          <p className=\"text-xs text-gray-600\">{studentData.email}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/profile\" className=\"flex items-center justify-center gap-3\">\r\n                      <UserCircle className=\"h-5 w-5\" />\r\n                      <span>Profile</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/wishlist\" className=\"flex items-center justify-center gap-3\">\r\n                      <ShoppingBag className=\"h-5 w-5\" />\r\n                      <span>My Wishlist</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    asChild\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white\"\r\n                    onClick={toggleMenu}\r\n                  >\r\n                    <Link href=\"/student/referral-dashboard\" className=\"flex items-center justify-center gap-3\">\r\n                      <Share2 className=\"h-5 w-5\" />\r\n                      <span>Referral Dashboard</span>\r\n                    </Link>\r\n                  </Button>\r\n                  <Link href=\"/coins\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <Image\r\n                            src=\"/uest_coin.png\"\r\n                            alt=\"Coin Icon\"\r\n                            width={20}\r\n                            height={20}\r\n                            className=\"object-contain\"\r\n                          />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">My Coins</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Link href=\"/student/chat\" passHref>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3\"\r\n                      onClick={toggleMenu}\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity\" />\r\n                      <div className=\"relative z-10 flex items-center justify-center gap-3\">\r\n                        <div className=\"p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500\">\r\n                          <MessageSquare className=\"h-5 w-5 text-white\" />\r\n                        </div>\r\n                        <span className=\"font-medium text-gray-300\">Chat</span>\r\n                      </div>\r\n                    </Button>\r\n                  </Link>\r\n\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <div className=\"flex items-center justify-center gap-3\">\r\n                      <User className=\"h-5 w-5\" />\r\n                      <span>Logout</span>\r\n                    </div>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-3 pt-3\">\r\n                  <Button\r\n                    variant=\"default\"\r\n                    className=\"w-full bg-orange-500 hover:bg-orange-600\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor/Classes Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"w-full border-customOrange text-orange-500 hover:bg-orange\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAG7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YACrB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC7B,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,MAAM;QACvB,IAAI,cAAc,iBAAiB,GAAG;QACtC,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;YAAc,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAAa;QAC9F;YAAE,MAAM;YAAU,OAAO;YAAY,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,OAAO;QAAK;QACtF;YACE,MAAM;YACN,qBACE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;kCAAK;;;;;;oBACL,mCAAqB,8OAAC,8IAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;YAGjE,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QAEA;YAAE,MAAM;YAAY,OAAO;YAAU,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAAa;KAC9E;IAED,MAAM,8BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;0BAA8C;;;;;;0BAG9D,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAkB;gBACnC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,GAAG;;oBAC9C;kCACW,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;IAKxC,qBACE;;0BACE,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,KAAK,IAAI;gDACT,KAAK,KAAK;gDACV,KAAK,KAAK,KAAK,+BACd,8OAAC;oDAAK,WAAU;8DAAiE;;;;;;gDAIlF,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAA+E;;;;;;;2CAZ5F,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC,8OAAC,6IAAA,CAAA,UAAgB;4CAAC,UAAS;4CAAQ,QAAQ,MAAM;;;;;;wCAElD,mCACC,8OAAC,6IAAA,CAAA,UAAgB;4CAAC,UAAS;4CAAU,QAAQ,aAAa;;;;;;sDAE5D,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;sDAER,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAAe,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,8OAAC,6IAAA,CAAA,UAAgB;oDAAC,UAAS;;;;;;8DAC3B;8DACA,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,QAAQ;kEACxB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;;8EAEV,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAI;wEACJ,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAMjC,8OAAC;4CAAI,WAAU;;;;;;wCAEd,iCACC,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;;;;;;8DAIV,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD;;;;;;;;;;;8EAGR,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa;;;;;;sFAEzB,8OAAC;4EAAE,WAAU;sFAAyB,MAAM,aAAa;;;;;;;;;;;;;;;;;;sEAI7D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,SAAS,IAAM;oEAAwB,WAAU;;sFACvD,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;sFAAK;;;;;;;;;;;;8EAQR,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAIV,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;wEACP,IAAI;4EACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4EAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gFACzB,OAAO,IAAI,CAAC;gFACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gFACjB,aAAa,UAAU,CAAC;gFACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4EAChB;wEACF,EAAE,OAAO,OAAO;4EACd,QAAQ,KAAK,CAAC,iBAAiB;4EAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wEACd;oEACF;8EACD;;;;;;;;;;;;;;;;;;;;;;;;wCAQR,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAe;;;;;;;;;;;wCAI7B,CAAC,mBAAmB,CAAC,mCACpB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;wCAI/B,mCACC;;8DACE,8OAAC,6IAAA,CAAA,UAAgB;oDAAC,UAAS;;;;;;8DAC3B;8DACA,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,QAAQ;kEACxB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;;8EAEV,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAI;wEACJ,OAAO;wEACP,QAAQ;wEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMpB,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;;;wCAMhC,mCACC,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;8DAIV,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,WAAU;8EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;8EAGR,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFACV,aAAa,aAAa,aAAa,WACpC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;sFAEN,8OAAC;4EAAE,WAAU;sFAAyB,aAAa,aAAa;;;;;;;;;;;;;;;;;;sEAIpE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAmB,WAAU;;0FACtC,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAoB,WAAU;;0FACvC,8OAAC,oNAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;0FACvB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;oEAAC,WAAU;8EACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAK;wEAA8B,WAAU;;0FACjD,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS;8EACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE;oCAAE;oCACX,cAAc,IAAM,cAAc;oCAClC,cAAc,IAAM,cAAc;;sDAElC,8OAAC;4CAAI,KAAK;4CAAY,WAAU;sDAC7B;;;;;;sDAEH,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;;kCACC,8OAAC;wBACC,WAAW,CAAC,mJAAmJ,EAC7J,aAAa,kBAAkB,oBAC/B;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI;wDACT,OAAO,KAAK,KAAK,KAAK,yBACrB,8OAAC;sEAAM,KAAK,KAAK;;;;;mEAEjB,KAAK,KAAK;;;;;;;gDAGb,KAAK,KAAK,KAAK,+BACd,8OAAC;oDAAK,WAAU;8DAA+E;;;;;;gDAIhG,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAAiE;;;;;;;2CAnB9E,KAAK,IAAI;;;;;;;;;;8CA2BpB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,QAAQ;8DACpC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM;;sEAEf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,4NAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;;;;;;8EAE7B,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;;;;;;;8DAkBhD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,QAAQ;8DAC/C,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAIlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;4DAClE,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,mCACC;;gDACG,aAAa,aAAa,aAAa,0BACtC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;0EAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,AAAC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAE,WAAW;;;;;;;;;;;0EAG1E,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA0B,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE;;;;;;kFACzF,8OAAC;wEAAE,WAAU;kFAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAK/D,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;;0EACtC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAoB,WAAU;;0EACvC,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO;oDACP,WAAU;oDACV,SAAS;8DAET,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,QAAQ;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4EACJ,KAAI;4EACJ,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;;;;;;;;;;;kFAGd,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,QAAQ;8DACjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;;;;;;kFAE3B,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;wCAMb,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;;;AAIzD;uCAEe", "debugId": null}}, {"offset": {"line": 3929, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/student/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect, useCallback, Suspense } from 'react';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { useForm } from 'react-hook-form';\r\nimport { z } from 'zod';\r\nimport { toast } from 'sonner';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { format } from 'date-fns';\r\nimport { Calendar as CalendarIcon, FileText, X, Camera, Upload, Check, CheckCircle, User, GraduationCap, Image as ImageIcon, FileCheck } from 'lucide-react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { RootState, AppDispatch } from '@/store';\r\nimport { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';\r\nimport { updateProfilePhoto } from '@/store/slices/studentProfileSlice';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport { cn } from '@/lib/utils';\r\nimport Image from 'next/image';\r\nimport Header from '../../../app-components/Header';\r\n\r\n// Enhanced schema with stricter validation for photo and document\r\nconst profileFormSchema = z.object({\r\n  firstName: z.string().min(2, 'First name must be at least 2 characters.'),\r\n  middleName: z.string().optional(),\r\n  lastName: z.string().min(2, 'Last name must be at least 2 characters.'),\r\n  mothersName: z.string().optional(),\r\n  email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),\r\n  contact: z\r\n    .string()\r\n    .min(10, 'Contact number must be at least 10 digits.')\r\n    .max(15, 'Contact number must not exceed 15 digits.')\r\n    .regex(/^\\d+$/, 'Contact number must contain only digits.'),\r\n  contactNo2: z\r\n    .string()\r\n    .min(10, 'Contact number must be at least 10 digits.')\r\n    .max(15, 'Contact number must not exceed 15 digits.')\r\n    .regex(/^\\d+$/, 'Contact number must contain only digits.')\r\n    .optional().or(z.literal('')),\r\n  medium: z.string().min(1, 'Medium of instruction is required'),\r\n  classroom: z.string().min(1, 'Standard is required'),\r\n  gender: z.string().min(1, 'Gender is required'),\r\n  birthday: z.date({ required_error: 'Please select your birthday' }),\r\n  school: z.string().min(2, 'School name must be at least 2 characters.'),\r\n  address: z.string().optional(),\r\n  age: z.string().optional(),\r\n  aadhaarNo: z.string().optional(),\r\n  bloodGroup: z.string().optional(),\r\n  birthPlace: z.string().optional(),\r\n  motherTongue: z.string().optional(),\r\n  religion: z.string().optional(),\r\n  caste: z.string().optional(),\r\n  subCaste: z.string().optional(),\r\n  photo: z.string().optional(), // Base64 string or URL\r\n  document: z.instanceof(File).optional().refine(\r\n    (file) => !file || file.size <= 5 * 1024 * 1024,\r\n    'Document size must not exceed 5MB'\r\n  ),\r\n});\r\n\r\ntype ProfileFormValues = z.infer<typeof profileFormSchema>;\r\n\r\ninterface DocumentWithUrl {\r\n  name: string;\r\n  size: number;\r\n  url: string;\r\n  type: string;\r\n}\r\n\r\nconst FORM_STEPS = [\r\n  { id: 'personal', title: 'Personal Information', icon: User, description: 'Basic personal details' },\r\n  { id: 'educational', title: 'Educational Information', icon: GraduationCap, description: 'Academic details' },\r\n  { id: 'photo', title: 'Profile Photo', icon: ImageIcon, description: 'Upload your photo' },\r\n  { id: 'document', title: 'Identity Document', icon: FileCheck, description: 'Verify your identity' },\r\n];\r\n\r\nconst StudentProfileContent = () => {\r\n  const router = useRouter();\r\n  const dispatch = useDispatch<AppDispatch>();\r\n  const searchParams = useSearchParams();\r\n  const fromQuiz = searchParams.get('quiz') === 'true';\r\n  const examId = searchParams.get('examId');\r\n\r\n  const [currentStep, setCurrentStep] = useState(0);\r\n  const [photo, setPhoto] = useState<string | null>(null);\r\n  const [isCameraOpen, setIsCameraOpen] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [cameraError, setCameraError] = useState<string | null>(null);\r\n  const [uploadedDocument, setUploadedDocument] = useState<File | DocumentWithUrl | null>(null);\r\n  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);\r\n\r\n  const { profileData, loading: profileLoading } = useSelector(\r\n    (state: RootState) => state.studentProfile\r\n  );\r\n  const classroomOptions = profileData?.classroomOptions || [];\r\n\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n\r\n  const form = useForm<ProfileFormValues>({\r\n    resolver: zodResolver(profileFormSchema),\r\n    defaultValues: {\r\n      firstName: '',\r\n      middleName: '',\r\n      lastName: '',\r\n      mothersName: '',\r\n      email: '',\r\n      contact: '',\r\n      contactNo2: '',\r\n      medium: '',\r\n      classroom: '',\r\n      gender: '',\r\n      birthday: undefined,\r\n      school: '',\r\n      address: '',\r\n      age: '',\r\n      aadhaarNo: '',\r\n      bloodGroup: '',\r\n      birthPlace: '',\r\n      motherTongue: '',\r\n      religion: '',\r\n      caste: '',\r\n      subCaste: '',\r\n      photo: '',\r\n      document: undefined,\r\n    },\r\n    mode: 'onSubmit',\r\n  });\r\n\r\n  // Authentication check\r\n  useEffect(() => {\r\n    const studentToken = localStorage.getItem('studentToken');\r\n    if (!studentToken) {\r\n      toast.error('Please login to access your profile');\r\n      router.push('/');\r\n    } else {\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n  }, [dispatch, router]);\r\n\r\n  // Populate form with profile data\r\n  useEffect(() => {\r\n    if (profileLoading || isCameraOpen || !profileData?.profile) return;\r\n\r\n    const profile = profileData.profile;\r\n    const studentData = profile.student || JSON.parse(localStorage.getItem('student_data') || '{}');\r\n\r\n    const formValues: ProfileFormValues = {\r\n      firstName: studentData.firstName || '',\r\n      middleName: studentData.middleName || '',\r\n      lastName: studentData.lastName || '',\r\n      mothersName: studentData.mothersName || '',\r\n      email: studentData.email || '',\r\n      contact: studentData.contact || '',\r\n      contactNo2: studentData.contactNo2 || '',\r\n      medium: profile.medium || '',\r\n      classroom: profile.classroom || '',\r\n      gender: studentData.gender || '',\r\n      birthday: studentData.birthday ? new Date(studentData.birthday) : new Date('2000-01-01'),\r\n      school: profile.school || '',\r\n      address: profile.address || '',\r\n      age: studentData.age || '',\r\n      aadhaarNo: studentData.aadhaarNo || '',\r\n      bloodGroup: studentData.bloodGroup || '',\r\n      birthPlace: studentData.birthPlace || '',\r\n      motherTongue: studentData.motherTongue || '',\r\n      religion: studentData.religion || '',\r\n      caste: studentData.caste || '',\r\n      subCaste: studentData.subCaste || '',\r\n      photo: profile.photo || '',\r\n      document: undefined,\r\n    };\r\n\r\n    if (profile.photo && !photo) {\r\n      setPhoto(profile.photo);\r\n      form.setValue('photo', profile.photo);\r\n    }\r\n\r\n    if (profile.documentUrl && !uploadedDocument && !isDocumentRemoved) {\r\n      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';\r\n      const documentUrl = profile.documentUrl.startsWith('http')\r\n        ? profile.documentUrl\r\n        : `${baseUrl}${profile.documentUrl}`;\r\n      setUploadedDocument({\r\n        name: documentUrl.split('/').pop() || 'Uploaded Document',\r\n        size: 0,\r\n        url: documentUrl,\r\n        type: 'application/octet-stream',\r\n      });\r\n      form.setValue('document', undefined);\r\n    }\r\n\r\n    form.reset(formValues);\r\n  }, [profileData, profileLoading, isCameraOpen, photo, uploadedDocument, isDocumentRemoved, form]);\r\n\r\n  // Step validation\r\n  const validatePersonalStep = useCallback(() => {\r\n    const values = form.getValues();\r\n    return !!(\r\n      values.firstName &&\r\n      values.lastName &&\r\n      values.contact &&\r\n      values.birthday &&\r\n      values.school\r\n    );\r\n  }, [form]);\r\n\r\n  const validateEducationalStep = useCallback(() => {\r\n    const values = form.getValues();\r\n    return !!(values.medium && values.classroom && values.gender);\r\n  }, [form]);\r\n\r\n  const validatePhotoStep = useCallback(() => {\r\n    return !!(photo || profileData?.profile?.photo);\r\n  }, [photo, profileData]);\r\n\r\n  const validateDocumentStep = useCallback(() => {\r\n    return !!uploadedDocument && !isDocumentRemoved;\r\n  }, [uploadedDocument, isDocumentRemoved]);\r\n\r\n  const isStepValid = useCallback(\r\n    (stepIndex: number) => {\r\n      switch (stepIndex) {\r\n        case 0:\r\n          return validatePersonalStep();\r\n        case 1:\r\n          return validateEducationalStep();\r\n        case 2:\r\n          return validatePhotoStep();\r\n        case 3:\r\n          return validateDocumentStep();\r\n        default:\r\n          return false;\r\n      }\r\n    },\r\n    [validatePersonalStep, validateEducationalStep, validatePhotoStep, validateDocumentStep]\r\n  );\r\n\r\n  const canProceedToStep = useCallback(\r\n    (stepIndex: number) => {\r\n      if (stepIndex === 0) return true;\r\n      for (let i = 0; i < stepIndex; i++) {\r\n        if (!isStepValid(i)) return false;\r\n      }\r\n      return true;\r\n    },\r\n    [isStepValid]\r\n  );\r\n\r\n  const goToStep = useCallback(\r\n    (stepIndex: number) => {\r\n      if (canProceedToStep(stepIndex)) {\r\n        setCurrentStep(stepIndex);\r\n      } else {\r\n        toast.error('Please complete all required fields in the previous steps.');\r\n      }\r\n    },\r\n    [canProceedToStep]\r\n  );\r\n\r\n  const openCamera = useCallback(async () => {\r\n    setCameraError(null);\r\n    try {\r\n      if (!navigator.mediaDevices?.getUserMedia) {\r\n        throw new Error('Camera not supported on this device');\r\n      }\r\n      setIsCameraOpen(true);\r\n      const stream = await navigator.mediaDevices.getUserMedia({\r\n        video: { facingMode: 'user' },\r\n      });\r\n      if (videoRef.current) {\r\n        videoRef.current.srcObject = stream;\r\n        videoRef.current.onloadedmetadata = () => {\r\n          videoRef.current?.play().catch(() => toast.error('Error starting camera preview'));\r\n        };\r\n      }\r\n    } catch (error: any) {\r\n      setIsCameraOpen(false);\r\n      const message =\r\n        error.name === 'NotAllowedError'\r\n          ? 'Please allow camera access in your browser settings.'\r\n          : 'Could not access camera. Please check your camera settings.';\r\n      setCameraError(message);\r\n      toast.error(message);\r\n    }\r\n  }, []);\r\n\r\n  const compressImage = useCallback((canvas: HTMLCanvasElement, maxWidth: number = 800, quality: number = 0.6): string => {\r\n    const context = canvas.getContext('2d');\r\n    if (!context) return '';\r\n\r\n    const originalWidth = canvas.width;\r\n    const originalHeight = canvas.height;\r\n    let newWidth = originalWidth;\r\n    let newHeight = originalHeight;\r\n\r\n    if (originalWidth > maxWidth) {\r\n      newWidth = maxWidth;\r\n      newHeight = (originalHeight * maxWidth) / originalWidth;\r\n    }\r\n\r\n    const compressedCanvas = document.createElement('canvas');\r\n    compressedCanvas.width = newWidth;\r\n    compressedCanvas.height = newHeight;\r\n\r\n    const compressedContext = compressedCanvas.getContext('2d');\r\n    if (!compressedContext) return '';\r\n\r\n    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);\r\n    return compressedCanvas.toDataURL('image/jpeg', quality);\r\n  }, []);\r\n\r\n  const capturePhoto = useCallback(() => {\r\n    if (!videoRef.current || !canvasRef.current) return;\r\n\r\n    const video = videoRef.current;\r\n    const canvas = canvasRef.current;\r\n    const context = canvas.getContext('2d');\r\n\r\n    canvas.width = video.videoWidth;\r\n    canvas.height = video.videoHeight;\r\n\r\n    context?.clearRect(0, 0, canvas.width, canvas.height);\r\n    context?.save();\r\n    context?.scale(-1, 1);\r\n    context?.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);\r\n    context?.restore();\r\n\r\n    const compressedPhotoDataUrl = compressImage(canvas);\r\n    const base64Data = compressedPhotoDataUrl.split(',')[1];\r\n    const sizeInKB = (base64Data.length * 3) / 4 / 1024;\r\n\r\n    if (sizeInKB > 5120) {\r\n      toast.error('Photo size exceeds 5MB limit. Please try again.');\r\n      return;\r\n    }\r\n\r\n    setPhoto(compressedPhotoDataUrl);\r\n    form.setValue('photo', compressedPhotoDataUrl);\r\n    dispatch(updateProfilePhoto(compressedPhotoDataUrl));\r\n    closeCamera();\r\n  }, [compressImage, dispatch, form]);\r\n\r\n  const closeCamera = useCallback(() => {\r\n    if (videoRef.current?.srcObject) {\r\n      const stream = videoRef.current.srcObject as MediaStream;\r\n      stream.getTracks().forEach(track => track.stop());\r\n      videoRef.current.srcObject = null;\r\n    }\r\n    setIsCameraOpen(false);\r\n    setCameraError(null);\r\n  }, []);\r\n\r\n  const removeDocument = useCallback(() => {\r\n    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {\r\n      URL.revokeObjectURL(uploadedDocument.url);\r\n    }\r\n    setUploadedDocument(null);\r\n    setIsDocumentRemoved(true);\r\n    form.setValue('document', undefined);\r\n  }, [uploadedDocument, form]);\r\n\r\n  const formatFileSize = useCallback((bytes: number) => {\r\n    if (bytes < 1024) return `${bytes} bytes`;\r\n    else if (bytes < 1048576) return `${(bytes / 1024).toFixed(1)} KB`;\r\n    else return `${(bytes / 1048576).toFixed(1)} MB`;\r\n  }, []);\r\n\r\n  const isFormValid = useCallback(() => {\r\n    return !!(\r\n      validatePersonalStep() &&\r\n      validateEducationalStep() &&\r\n      validatePhotoStep() &&\r\n      validateDocumentStep()\r\n    );\r\n  }, [validatePersonalStep, validateEducationalStep, validatePhotoStep, validateDocumentStep]);\r\n\r\n  const onSubmit = async (data: ProfileFormValues) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      if (!validatePhotoStep()) {\r\n        toast.error('Please capture or upload a profile photo.');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      if (!validateDocumentStep()) {\r\n        toast.error('Please upload an identity document.');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      if (!(await form.trigger())) {\r\n        toast.error('Please fill in all required fields correctly.');\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      const jsonData: any = {\r\n        ...data,\r\n        birthday: data.birthday?.toISOString() || '',\r\n      };\r\n\r\n      if (data.photo?.startsWith('data:')) {\r\n        const base64Data = data.photo.split(',')[1];\r\n        jsonData.photo = base64Data;\r\n        jsonData.photoMimeType = 'image/jpeg';\r\n      }\r\n\r\n      if (data.document instanceof File) {\r\n        const documentBase64 = await new Promise<string>((resolve, reject) => {\r\n          const reader = new FileReader();\r\n          reader.onload = () => resolve((reader.result as string).split(',')[1]);\r\n          reader.onerror = reject;\r\n          if (data.document) {\r\n            reader.readAsDataURL(data.document);\r\n          } else {\r\n            reject(new Error('No document file provided'));\r\n          }\r\n        });\r\n        jsonData.document = documentBase64;\r\n        jsonData.documentMimeType = data.document.type;\r\n        jsonData.documentName = data.document.name;\r\n      }\r\n\r\n      if (isDocumentRemoved && profileData?.profile?.documentUrl) {\r\n        jsonData.removeDocument = true;\r\n      }\r\n\r\n      const studentToken = localStorage.getItem('studentToken');\r\n      if (!studentToken) {\r\n        toast.error('Please login to submit your profile');\r\n        router.push('/');\r\n        return;\r\n      }\r\n\r\n      const result = await dispatch(updateStudentProfile(jsonData));\r\n\r\n      if (result.meta.requestStatus === 'fulfilled') {\r\n        toast.success(`Profile ${profileData?.profile ? 'updated' : 'created'} successfully!`);\r\n        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');\r\n        const studentData = {\r\n          ...existingStudentData,\r\n          id: existingStudentData.id || profileData?.profile?.student?.id || '',\r\n          firstName: data.firstName,\r\n          lastName: data.lastName,\r\n          email: existingStudentData.email || profileData?.profile?.student?.email || '',\r\n          contact: data.contact,\r\n        };\r\n        localStorage.setItem('student_data', JSON.stringify(studentData));\r\n        setIsDocumentRemoved(false);\r\n        await dispatch(fetchStudentProfile());\r\n\r\n        if (fromQuiz && examId) {\r\n          router.push(`/uwhiz-exam/${examId}`);\r\n        } else if (fromQuiz) {\r\n          router.push('/mock-test');\r\n        } else {\r\n          router.push('/');\r\n        }\r\n      } else {\r\n        const errorMessage = result.payload as string;\r\n        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {\r\n          toast.error('Your session has expired. Please login again.');\r\n          localStorage.removeItem('studentToken');\r\n          router.push('/');\r\n        } else {\r\n          toast.error(errorMessage || 'Failed to update profile');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      toast.error('Failed to submit profile information');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Header />\r\n      <div className=\"space-y-6 p-10 pb-4 md:block\">\r\n        <div className=\"space-y-0.5\">\r\n          <h2 className=\"text-2xl font-bold tracking-tight\">Student Profile</h2>\r\n          <p className=\"text-muted-foreground\">\r\n            Complete your profile information. Your progress will be automatically saved as you complete each section.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n          <div\r\n            className=\"bg-black h-2 rounded-full transition-all duration-300 ease-in-out\"\r\n            style={{ width: `${((currentStep + 1) / FORM_STEPS.length) * 100}%` }}\r\n          />\r\n        </div>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          {Math.round(((currentStep + 1) / FORM_STEPS.length) * 100)}% complete\r\n        </p>\r\n\r\n        <div className=\"my-6 border-t border-gray-200\" />\r\n\r\n        {profileLoading ? (\r\n          <div className=\"flex flex-col items-center justify-center py-12\">\r\n            <svg\r\n              className=\"animate-spin h-10 w-10 text-black mb-4\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n            >\r\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n              <path\r\n                className=\"opacity-75\"\r\n                fill=\"currentColor\"\r\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n              />\r\n            </svg>\r\n            <p className=\"text-gray-600\">Loading profile information...</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0\">\r\n            <aside className=\"-mx-4 lg:w-1/6 pb-12\">\r\n              <nav className=\"space-y-1\">\r\n                {FORM_STEPS.map((step, index) => {\r\n                  const isActive = currentStep === index;\r\n                  const isCompleted = isStepValid(index);\r\n                  const canAccess = canProceedToStep(index);\r\n\r\n                  return (\r\n                    <button\r\n                      key={step.id}\r\n                      onClick={() => goToStep(index)}\r\n                      disabled={!canAccess}\r\n                      className={cn(\r\n                        'flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors',\r\n                        isActive\r\n                          ? 'bg-muted text-primary'\r\n                          : !canAccess\r\n                          ? 'text-gray-400 cursor-not-allowed'\r\n                          : 'text-muted-foreground hover:text-primary'\r\n                      )}\r\n                    >\r\n                      <span>{step.title}</span>\r\n                      {isCompleted && <CheckCircle size={16} className=\"text-green-500\" />}\r\n                    </button>\r\n                  );\r\n                })}\r\n              </nav>\r\n            </aside>\r\n            <div className=\"flex justify-center w-full\">\r\n              <div className=\"flex-1 lg:max-w-2xl pb-12\">\r\n                <Form {...form}>\r\n                  <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\r\n                    {currentStep === 0 && (\r\n                      <Card className=\"shadow-sm\">\r\n                        <CardHeader>\r\n                          <CardTitle className=\"text-lg font-medium\">Personal Information</CardTitle>\r\n                          <CardDescription>Basic personal details</CardDescription>\r\n                        </CardHeader>\r\n                        <CardContent className=\"space-y-6\">\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"firstName\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>First Name *</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter First Name\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"middleName\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Middle Name</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Middle Name\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"lastName\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Last Name *</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Last Name\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"mothersName\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Mother's Name</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Mother's Name\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"email\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel>Email</FormLabel>\r\n                                <FormControl>\r\n                                  <Input placeholder=\"Enter Email\" {...field} disabled />\r\n                                </FormControl>\r\n                                <FormDescription>Email cannot be changed</FormDescription>\r\n                                <FormMessage />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"contact\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Contact Number *</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"8520369851\" {...field} type=\"tel\" />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"contactNo2\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Contact Number 2</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Alternate Number\" {...field} type=\"tel\" />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"birthday\"\r\n                            render={({ field }) => (\r\n                              <FormItem className=\"flex flex-col\">\r\n                                <FormLabel>Date of Birth *</FormLabel>\r\n                                <Popover>\r\n                                  <PopoverTrigger asChild>\r\n                                    <FormControl>\r\n                                      <Button\r\n                                        variant=\"outline\"\r\n                                        className={cn(\r\n                                          'w-full pl-3 text-left font-normal',\r\n                                          !field.value && 'text-muted-foreground'\r\n                                        )}\r\n                                      >\r\n                                        {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (\r\n                                          format(field.value, 'PPP')\r\n                                        ) : (\r\n                                          <span>Select your birthday</span>\r\n                                        )}\r\n                                        <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\r\n                                      </Button>\r\n                                    </FormControl>\r\n                                  </PopoverTrigger>\r\n                                  <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                                    <Calendar\r\n                                      mode=\"single\"\r\n                                      selected={field.value}\r\n                                      onSelect={field.onChange}\r\n                                      disabled={(date) => date > new Date() || date < new Date('1900-01-01')}\r\n                                      initialFocus\r\n                                    />\r\n                                  </PopoverContent>\r\n                                </Popover>\r\n                                <FormMessage />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"school\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel>School Name *</FormLabel>\r\n                                <FormControl>\r\n                                  <Input placeholder=\"Enter School\" {...field} />\r\n                                </FormControl>\r\n                                <FormMessage />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"address\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                <FormLabel>Address</FormLabel>\r\n                                <FormControl>\r\n                                  <Textarea placeholder=\"Enter Address\" {...field} />\r\n                                </FormControl>\r\n                                <FormMessage />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"age\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Age</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Age\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"aadhaarNo\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Aadhaar Number</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Aadhaar No\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"bloodGroup\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Blood Group</FormLabel>\r\n                                  <Select onValueChange={field.onChange} value={field.value}>\r\n                                    <FormControl>\r\n                                      <SelectTrigger>\r\n                                        <SelectValue placeholder=\"Select\" />\r\n                                      </SelectTrigger>\r\n                                    </FormControl>\r\n                                    <SelectContent>\r\n                                      <SelectItem value=\"A+\">A+</SelectItem>\r\n                                      <SelectItem value=\"A-\">A-</SelectItem>\r\n                                      <SelectItem value=\"B+\">B+</SelectItem>\r\n                                      <SelectItem value=\"B-\">B-</SelectItem>\r\n                                      <SelectItem value=\"AB+\">AB+</SelectItem>\r\n                                      <SelectItem value=\"AB-\">AB-</SelectItem>\r\n                                      <SelectItem value=\"O+\">O+</SelectItem>\r\n                                      <SelectItem value=\"O-\">O-</SelectItem>\r\n                                    </SelectContent>\r\n                                  </Select>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"birthPlace\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Birth Place</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Birth Place\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"motherTongue\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Mother Tongue</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Mother Tongue\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"religion\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Religion</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Religion\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"caste\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Caste</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Caste\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"subCaste\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Sub Caste</FormLabel>\r\n                                  <FormControl>\r\n                                    <Input placeholder=\"Enter Sub Caste\" {...field} />\r\n                                  </FormControl>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n                    {currentStep === 1 && (\r\n                      <Card className=\"shadow-sm\">\r\n                        <CardHeader>\r\n                          <CardTitle className=\"text-lg font-medium\">Educational Information</CardTitle>\r\n                          <CardDescription>Academic details</CardDescription>\r\n                        </CardHeader>\r\n                        <CardContent className=\"space-y-6\">\r\n                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"medium\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Medium *</FormLabel>\r\n                                  <Select onValueChange={field.onChange} value={field.value}>\r\n                                    <FormControl>\r\n                                      <SelectTrigger>\r\n                                        <SelectValue placeholder=\"Select\" />\r\n                                      </SelectTrigger>\r\n                                    </FormControl>\r\n                                    <SelectContent>\r\n                                      <SelectItem value=\"english\">English</SelectItem>\r\n                                      <SelectItem value=\"gujarati\">Gujarati</SelectItem>\r\n                                    </SelectContent>\r\n                                  </Select>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"classroom\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Standard *</FormLabel>\r\n                                  <Select onValueChange={field.onChange} value={field.value}>\r\n                                    <FormControl>\r\n                                      <SelectTrigger>\r\n                                        <SelectValue placeholder=\"Select\" />\r\n                                      </SelectTrigger>\r\n                                    </FormControl>\r\n                                    <SelectContent>\r\n                                      {profileLoading ? (\r\n                                        <div className=\"flex items-center justify-center p-4\">\r\n                                          <div className=\"animate-spin h-5 w-5 border-2 border-gray-300 border-t-black rounded-full\" />\r\n                                        </div>\r\n                                      ) : classroomOptions.length > 0 ? (\r\n                                        classroomOptions.map((option) => (\r\n                                          <SelectItem key={option.id} value={option.value}>\r\n                                            {option.value}\r\n                                          </SelectItem>\r\n                                        ))\r\n                                      ) : (\r\n                                        <div className=\"p-2 text-center text-gray-500\">No classroom options available</div>\r\n                                      )}\r\n                                    </SelectContent>\r\n                                  </Select>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                            <FormField\r\n                              control={form.control}\r\n                              name=\"gender\"\r\n                              render={({ field }) => (\r\n                                <FormItem>\r\n                                  <FormLabel>Gender *</FormLabel>\r\n                                  <Select onValueChange={field.onChange} value={field.value}>\r\n                                    <FormControl>\r\n                                      <SelectTrigger>\r\n                                        <SelectValue placeholder=\"Select\" />\r\n                                      </SelectTrigger>\r\n                                    </FormControl>\r\n                                    <SelectContent>\r\n                                      <SelectItem value=\"male\">Male</SelectItem>\r\n                                      <SelectItem value=\"female\">Female</SelectItem>\r\n                                      <SelectItem value=\"other\">Other</SelectItem>\r\n                                    </SelectContent>\r\n                                  </Select>\r\n                                  <FormMessage />\r\n                                </FormItem>\r\n                              )}\r\n                            />\r\n                          </div>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n                    {currentStep === 2 && (\r\n                      <Card className=\"shadow-sm\">\r\n                        <CardHeader>\r\n                          <CardTitle className=\"text-lg font-medium\">Profile Photo</CardTitle>\r\n                          <CardDescription>Take a clear photo of your face for your profile (MAX. 5MB)</CardDescription>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"photo\"\r\n                            render={() => (\r\n                              <FormItem>\r\n                                <FormControl>\r\n                                  <div>\r\n                                    {cameraError && (\r\n                                      <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\r\n                                        <p className=\"text-red-700 text-sm\">{cameraError}</p>\r\n                                      </div>\r\n                                    )}\r\n                                    {!isCameraOpen && !photo && (\r\n                                      <Button\r\n                                        type=\"button\"\r\n                                        onClick={openCamera}\r\n                                        className=\"w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2\"\r\n                                      >\r\n                                        <Camera className=\"h-5 w-5 mr-2\" />\r\n                                        Open Camera\r\n                                      </Button>\r\n                                    )}\r\n                                    {isCameraOpen && (\r\n                                      <div className=\"camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm\">\r\n                                        <video\r\n                                          ref={videoRef}\r\n                                          autoPlay\r\n                                          playsInline\r\n                                          className=\"w-full h-auto transform scale-x-[-1]\"\r\n                                        />\r\n                                        <div className=\"flex p-4 bg-gray-50\">\r\n                                          <Button\r\n                                            type=\"button\"\r\n                                            onClick={capturePhoto}\r\n                                            variant=\"default\"\r\n                                            className=\"flex-1 mr-2 bg-black hover:bg-gray-800 text-white\"\r\n                                          >\r\n                                            <Check className=\"h-4 w-4 mr-2\" />\r\n                                            Capture\r\n                                          </Button>\r\n                                          <Button\r\n                                            type=\"button\"\r\n                                            onClick={closeCamera}\r\n                                            variant=\"outline\"\r\n                                            className=\"flex-1 border-gray-300\"\r\n                                          >\r\n                                            <X className=\"h-4 w-4 mr-2\" />\r\n                                            Cancel\r\n                                          </Button>\r\n                                        </div>\r\n                                      </div>\r\n                                    )}\r\n                                    {!isCameraOpen && (photo || profileData?.profile?.photo) && (\r\n                                      <div className=\"flex flex-col sm:flex-row items-center gap-4\">\r\n                                        <div className=\"border rounded-lg shadow-md bg-gray-50 p-4 max-w-full\">\r\n                                          <div className=\"flex justify-center\">\r\n                                            {(() => {\r\n                                              const displayPhoto = photo || profileData?.profile?.photo;\r\n                                              if (displayPhoto) {\r\n                                                return (\r\n                                                  <Image\r\n                                                    src={\r\n                                                      displayPhoto.startsWith('data:')\r\n                                                        ? displayPhoto\r\n                                                        : displayPhoto.startsWith('http')\r\n                                                        ? displayPhoto\r\n                                                        : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`\r\n                                                    }\r\n                                                    alt=\"Student Photo\"\r\n                                                    height={1000}\r\n                                                    width={1000}\r\n                                                    className=\"max-w-full max-h-80 object-contain rounded-lg\"\r\n                                                    style={{ height: 'auto', width: 'auto' }}\r\n                                                    unoptimized={displayPhoto.startsWith('data:')}\r\n                                                  />\r\n                                                );\r\n                                              }\r\n                                              return (\r\n                                                <div className=\"flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg\">\r\n                                                  <Camera className=\"h-12 w-12 text-gray-400\" />\r\n                                                </div>\r\n                                              );\r\n                                            })()}\r\n                                          </div>\r\n                                        </div>\r\n                                        <Button\r\n                                          type=\"button\"\r\n                                          onClick={() => {\r\n                                            setPhoto(null);\r\n                                            setCameraError(null);\r\n                                            dispatch(updateProfilePhoto(undefined));\r\n                                            form.setValue('photo', '');\r\n                                            openCamera();\r\n                                          }}\r\n                                          variant=\"outline\"\r\n                                          className=\"border-gray-300\"\r\n                                        >\r\n                                          <Camera className=\"h-4 w-4 mr-2\" />\r\n                                          Retake Photo\r\n                                        </Button>\r\n                                      </div>\r\n                                    )}\r\n                                    <canvas ref={canvasRef} style={{ display: 'none' }} />\r\n                                  </div>\r\n                                </FormControl>\r\n                                <FormDescription className=\"text-xs text-gray-500 mt-2\">\r\n                                  A clear photo helps us identify you and personalize your profile\r\n                                </FormDescription>\r\n                                <FormMessage />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n                    {currentStep === 3 && (\r\n                      <Card className=\"shadow-sm\">\r\n                        <CardHeader>\r\n                          <CardTitle className=\"text-lg font-medium\">Identity Document</CardTitle>\r\n                          <CardDescription>\r\n                            Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate\r\n                          </CardDescription>\r\n                        </CardHeader>\r\n                        <CardContent>\r\n                          <FormField\r\n                            control={form.control}\r\n                            name=\"document\"\r\n                            render={({ field }) => (\r\n                              <FormItem>\r\n                                {!uploadedDocument ? (\r\n                                  <FormControl>\r\n                                    <div className=\"flex items-center justify-center w-full\">\r\n                                      <label className=\"flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors\">\r\n                                        <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\r\n                                          <Upload className=\"w-10 h-10 mb-3 text-black\" />\r\n                                          <p className=\"mb-2 text-sm text-gray-700\">\r\n                                            <span className=\"font-semibold\">Click to upload</span> or drag and drop\r\n                                          </p>\r\n                                          <p className=\"text-xs text-gray-500\">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>\r\n                                        </div>\r\n                                        <Input\r\n                                          id=\"document\"\r\n                                          type=\"file\"\r\n                                          accept=\".pdf,.jpg,.jpeg,.png\"\r\n                                          className=\"hidden\"\r\n                                          onChange={(e) => {\r\n                                            const file = e.target.files?.[0];\r\n                                            if (file) {\r\n                                              if (file.size > 5 * 1024 * 1024) {\r\n                                                toast.error('File size exceeds 5MB limit');\r\n                                                return;\r\n                                              }\r\n                                              const documentWithUrl = {\r\n                                                name: file.name,\r\n                                                size: file.size,\r\n                                                type: file.type,\r\n                                                url: URL.createObjectURL(file),\r\n                                              };\r\n                                              setUploadedDocument(documentWithUrl);\r\n                                              setIsDocumentRemoved(false);\r\n                                              field.onChange(file);\r\n                                            }\r\n                                          }}\r\n                                        />\r\n                                      </label>\r\n                                    </div>\r\n                                  </FormControl>\r\n                                ) : (\r\n                                  <div className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                      <div className=\"flex items-center space-x-3\">\r\n                                        <div className=\"p-2 bg-[#fff8f3] rounded-full\">\r\n                                          <FileText className=\"h-5 w-5 text-black\" />\r\n                                        </div>\r\n                                        <div>\r\n                                          <p className=\"text-sm font-medium text-gray-700\">{uploadedDocument.name}</p>\r\n                                          <p className=\"text-xs text-gray-500\">\r\n                                            {uploadedDocument instanceof File\r\n                                              ? formatFileSize(uploadedDocument.size)\r\n                                              : 'Previously uploaded document'}\r\n                                          </p>\r\n                                        </div>\r\n                                      </div>\r\n                                      <div className=\"flex space-x-2\">\r\n                                        {uploadedDocument && 'url' in uploadedDocument && (\r\n                                          <Button\r\n                                            type=\"button\"\r\n                                            variant=\"outline\"\r\n                                            size=\"sm\"\r\n                                            onClick={() => window.open(uploadedDocument.url, '_blank')}\r\n                                            className=\"h-8 px-3 border-gray-200\"\r\n                                          >\r\n                                            View\r\n                                          </Button>\r\n                                        )}\r\n                                        <Button\r\n                                          type=\"button\"\r\n                                          variant=\"outline\"\r\n                                          size=\"sm\"\r\n                                          onClick={removeDocument}\r\n                                          className=\"h-8 w-8 p-0 border-gray-200\"\r\n                                        >\r\n                                          <X className=\"h-4 w-4 text-gray-500\" />\r\n                                        </Button>\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                                )}\r\n                                <FormDescription className=\"text-xs text-gray-500 mt-2\">\r\n                                  This document will serve to verify your identity and date of birth.\r\n                                </FormDescription>\r\n                                <FormMessage />\r\n                              </FormItem>\r\n                            )}\r\n                          />\r\n                        </CardContent>\r\n                      </Card>\r\n                    )}\r\n                    <div className=\"flex flex-col sm:flex-row justify-between gap-4 pt-6\">\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}\r\n                        disabled={currentStep === 0}\r\n                        className=\"px-6 order-2 sm:order-1\"\r\n                      >\r\n                        Previous\r\n                      </Button>\r\n                      {currentStep < FORM_STEPS.length - 1 ? (\r\n                        <div className=\"flex flex-col items-end order-1 sm:order-2\">\r\n                          {!isStepValid(currentStep) && (\r\n                            <p className=\"text-xs text-red-500 mb-2\">\r\n                              Please complete all required fields\r\n                            </p>\r\n                          )}\r\n                          <Button\r\n                            type=\"button\"\r\n                            onClick={() => goToStep(currentStep + 1)}\r\n                            disabled={!isStepValid(currentStep)}\r\n                            className=\"px-6 bg-black text-white hover:bg-gray-800 disabled:bg-gray-300\"\r\n                          >\r\n                            Next\r\n                          </Button>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"flex flex-col items-end order-1 sm:order-2\">\r\n                          {!isFormValid() && (\r\n                            <p className=\"text-xs text-red-500 mb-2\">\r\n                              Please complete all steps to save your profile\r\n                            </p>\r\n                          )}\r\n                          <Button\r\n                            type=\"submit\"\r\n                            className={cn(\r\n                              'px-6 font-medium py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200',\r\n                              isFormValid() && !isSubmitting\r\n                                ? 'bg-black text-white hover:bg-gray-800'\r\n                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n                            )}\r\n                            disabled={isSubmitting || !isFormValid()}\r\n                          >\r\n                            {isSubmitting ? 'Saving...' : 'Save Profile'}\r\n                          </Button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </form>\r\n                </Form>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nconst StudentProfilePage = () => {\r\n  return (\r\n    <Suspense\r\n      fallback={\r\n        <div className=\"min-h-screen flex items-center justify-center\">\r\n          <svg\r\n            className=\"animate-spin h-10 w-10 text-black\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n            <path\r\n              className=\"opacity-75\"\r\n              fill=\"currentColor\"\r\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n            />\r\n          </svg>\r\n        </div>\r\n      }\r\n    >\r\n      <StudentProfileContent />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default StudentProfilePage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AASA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAtCA;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,kEAAkE;AAClE,MAAM,oBAAoB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,sCAAsC,QAAQ,GAAG,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACtF,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,IAAI,6CACR,KAAK,CAAC,SAAS;IAClB,YAAY,oIAAA,CAAA,IAAC,CACV,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,IAAI,6CACR,KAAK,CAAC,SAAS,4CACf,QAAQ,GAAG,EAAE,CAAC,oIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC3B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,UAAU,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAE,gBAAgB;IAA8B;IACjE,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,KAAK,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACxB,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,oIAAA,CAAA,IAAC,CAAC,UAAU,CAAC,MAAM,QAAQ,GAAG,MAAM,CAC5C,CAAC,OAAS,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,OAAO,MAC3C;AAEJ;AAWA,MAAM,aAAa;IACjB;QAAE,IAAI;QAAY,OAAO;QAAwB,MAAM,kMAAA,CAAA,OAAI;QAAE,aAAa;IAAyB;IACnG;QAAE,IAAI;QAAe,OAAO;QAA2B,MAAM,wNAAA,CAAA,gBAAa;QAAE,aAAa;IAAmB;IAC5G;QAAE,IAAI;QAAS,OAAO;QAAiB,MAAM,oMAAA,CAAA,QAAS;QAAE,aAAa;IAAoB;IACzF;QAAE,IAAI;QAAY,OAAO;QAAqB,MAAM,gNAAA,CAAA,YAAS;QAAE,aAAa;IAAuB;CACpG;AAED,MAAM,wBAAwB;IAC5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,aAAa,GAAG,CAAC,YAAY;IAC9C,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IACxF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,EAAE,WAAW,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACzD,CAAC,QAAqB,MAAM,cAAc;IAE5C,MAAM,mBAAmB,aAAa,oBAAoB,EAAE;IAE5D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAqB;QACtC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW;YACX,YAAY;YACZ,UAAU;YACV,aAAa;YACb,OAAO;YACP,SAAS;YACT,YAAY;YACZ,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,KAAK;YACL,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA,MAAM;IACR;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;IACF,GAAG;QAAC;QAAU;KAAO;IAErB,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,gBAAgB,CAAC,aAAa,SAAS;QAE7D,MAAM,UAAU,YAAY,OAAO;QACnC,MAAM,cAAc,QAAQ,OAAO,IAAI,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;QAE1F,MAAM,aAAgC;YACpC,WAAW,YAAY,SAAS,IAAI;YACpC,YAAY,YAAY,UAAU,IAAI;YACtC,UAAU,YAAY,QAAQ,IAAI;YAClC,aAAa,YAAY,WAAW,IAAI;YACxC,OAAO,YAAY,KAAK,IAAI;YAC5B,SAAS,YAAY,OAAO,IAAI;YAChC,YAAY,YAAY,UAAU,IAAI;YACtC,QAAQ,QAAQ,MAAM,IAAI;YAC1B,WAAW,QAAQ,SAAS,IAAI;YAChC,QAAQ,YAAY,MAAM,IAAI;YAC9B,UAAU,YAAY,QAAQ,GAAG,IAAI,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;YAC3E,QAAQ,QAAQ,MAAM,IAAI;YAC1B,SAAS,QAAQ,OAAO,IAAI;YAC5B,KAAK,YAAY,GAAG,IAAI;YACxB,WAAW,YAAY,SAAS,IAAI;YACpC,YAAY,YAAY,UAAU,IAAI;YACtC,YAAY,YAAY,UAAU,IAAI;YACtC,cAAc,YAAY,YAAY,IAAI;YAC1C,UAAU,YAAY,QAAQ,IAAI;YAClC,OAAO,YAAY,KAAK,IAAI;YAC5B,UAAU,YAAY,QAAQ,IAAI;YAClC,OAAO,QAAQ,KAAK,IAAI;YACxB,UAAU;QACZ;QAEA,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO;YAC3B,SAAS,QAAQ,KAAK;YACtB,KAAK,QAAQ,CAAC,SAAS,QAAQ,KAAK;QACtC;QAEA,IAAI,QAAQ,WAAW,IAAI,CAAC,oBAAoB,CAAC,mBAAmB;YAClE,MAAM,UAAU,8DAAwC;YACxD,MAAM,cAAc,QAAQ,WAAW,CAAC,UAAU,CAAC,UAC/C,QAAQ,WAAW,GACnB,GAAG,UAAU,QAAQ,WAAW,EAAE;YACtC,oBAAoB;gBAClB,MAAM,YAAY,KAAK,CAAC,KAAK,GAAG,MAAM;gBACtC,MAAM;gBACN,KAAK;gBACL,MAAM;YACR;YACA,KAAK,QAAQ,CAAC,YAAY;QAC5B;QAEA,KAAK,KAAK,CAAC;IACb,GAAG;QAAC;QAAa;QAAgB;QAAc;QAAO;QAAkB;QAAmB;KAAK;IAEhG,kBAAkB;IAClB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,MAAM,SAAS,KAAK,SAAS;QAC7B,OAAO,CAAC,CAAC,CACP,OAAO,SAAS,IAChB,OAAO,QAAQ,IACf,OAAO,OAAO,IACd,OAAO,QAAQ,IACf,OAAO,MAAM,AACf;IACF,GAAG;QAAC;KAAK;IAET,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,MAAM,SAAS,KAAK,SAAS;QAC7B,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM,IAAI,OAAO,SAAS,IAAI,OAAO,MAAM;IAC9D,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,OAAO,CAAC,CAAC,CAAC,SAAS,aAAa,SAAS,KAAK;IAChD,GAAG;QAAC;QAAO;KAAY;IAEvB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,OAAO,CAAC,CAAC,oBAAoB,CAAC;IAChC,GAAG;QAAC;QAAkB;KAAkB;IAExC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF,GACA;QAAC;QAAsB;QAAyB;QAAmB;KAAqB;IAG1F,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC;QACC,IAAI,cAAc,GAAG,OAAO;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAClC,IAAI,CAAC,YAAY,IAAI,OAAO;QAC9B;QACA,OAAO;IACT,GACA;QAAC;KAAY;IAGf,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzB,CAAC;QACC,IAAI,iBAAiB,YAAY;YAC/B,eAAe;QACjB,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GACA;QAAC;KAAiB;IAGpB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,eAAe;QACf,IAAI;YACF,IAAI,CAAC,UAAU,YAAY,EAAE,cAAc;gBACzC,MAAM,IAAI,MAAM;YAClB;YACA,gBAAgB;YAChB,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBAAE,YAAY;gBAAO;YAC9B;YACA,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAC7B,SAAS,OAAO,CAAC,gBAAgB,GAAG;oBAClC,SAAS,OAAO,EAAE,OAAO,MAAM,IAAM,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACnD;YACF;QACF,EAAE,OAAO,OAAY;YACnB,gBAAgB;YAChB,MAAM,UACJ,MAAM,IAAI,KAAK,oBACX,yDACA;YACN,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA2B,WAAmB,GAAG,EAAE,UAAkB,GAAG;QACzG,MAAM,UAAU,OAAO,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,gBAAgB,OAAO,KAAK;QAClC,MAAM,iBAAiB,OAAO,MAAM;QACpC,IAAI,WAAW;QACf,IAAI,YAAY;QAEhB,IAAI,gBAAgB,UAAU;YAC5B,WAAW;YACX,YAAY,AAAC,iBAAiB,WAAY;QAC5C;QAEA,MAAM,mBAAmB,SAAS,aAAa,CAAC;QAChD,iBAAiB,KAAK,GAAG;QACzB,iBAAiB,MAAM,GAAG;QAE1B,MAAM,oBAAoB,iBAAiB,UAAU,CAAC;QACtD,IAAI,CAAC,mBAAmB,OAAO;QAE/B,kBAAkB,SAAS,CAAC,QAAQ,GAAG,GAAG,UAAU;QACpD,OAAO,iBAAiB,SAAS,CAAC,cAAc;IAClD,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;QAE7C,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,UAAU,OAAO,UAAU,CAAC;QAElC,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,SAAS,UAAU,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACpD,SAAS;QACT,SAAS,MAAM,CAAC,GAAG;QACnB,SAAS,UAAU,OAAO,CAAC,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACvE,SAAS;QAET,MAAM,yBAAyB,cAAc;QAC7C,MAAM,aAAa,uBAAuB,KAAK,CAAC,IAAI,CAAC,EAAE;QACvD,MAAM,WAAW,AAAC,WAAW,MAAM,GAAG,IAAK,IAAI;QAE/C,IAAI,WAAW,MAAM;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,SAAS;QACT,KAAK,QAAQ,CAAC,SAAS;QACvB,SAAS,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B;IACF,GAAG;QAAC;QAAe;QAAU;KAAK;IAElC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,SAAS,OAAO,EAAE,WAAW;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;QACA,gBAAgB;QAChB,eAAe;IACjB,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,oBAAoB,SAAS,oBAAoB,iBAAiB,GAAG,CAAC,UAAU,CAAC,UAAU;YAC7F,IAAI,eAAe,CAAC,iBAAiB,GAAG;QAC1C;QACA,oBAAoB;QACpB,qBAAqB;QACrB,KAAK,QAAQ,CAAC,YAAY;IAC5B,GAAG;QAAC;QAAkB;KAAK;IAE3B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,IAAI,QAAQ,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;aACpC,IAAI,QAAQ,SAAS,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;aAC7D,OAAO,GAAG,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;IAClD,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,OAAO,CAAC,CAAC,CACP,0BACA,6BACA,uBACA,sBACF;IACF,GAAG;QAAC;QAAsB;QAAyB;QAAmB;KAAqB;IAE3F,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,IAAI;YACF,IAAI,CAAC,qBAAqB;gBACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,IAAI,CAAC,wBAAwB;gBAC3B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,IAAI,CAAE,MAAM,KAAK,OAAO,IAAK;gBAC3B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,gBAAgB;gBAChB;YACF;YAEA,MAAM,WAAgB;gBACpB,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,EAAE,iBAAiB;YAC5C;YAEA,IAAI,KAAK,KAAK,EAAE,WAAW,UAAU;gBACnC,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3C,SAAS,KAAK,GAAG;gBACjB,SAAS,aAAa,GAAG;YAC3B;YAEA,IAAI,KAAK,QAAQ,YAAY,MAAM;gBACjC,MAAM,iBAAiB,MAAM,IAAI,QAAgB,CAAC,SAAS;oBACzD,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,AAAC,OAAO,MAAM,CAAY,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrE,OAAO,OAAO,GAAG;oBACjB,IAAI,KAAK,QAAQ,EAAE;wBACjB,OAAO,aAAa,CAAC,KAAK,QAAQ;oBACpC,OAAO;wBACL,OAAO,IAAI,MAAM;oBACnB;gBACF;gBACA,SAAS,QAAQ,GAAG;gBACpB,SAAS,gBAAgB,GAAG,KAAK,QAAQ,CAAC,IAAI;gBAC9C,SAAS,YAAY,GAAG,KAAK,QAAQ,CAAC,IAAI;YAC5C;YAEA,IAAI,qBAAqB,aAAa,SAAS,aAAa;gBAC1D,SAAS,cAAc,GAAG;YAC5B;YAEA,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,CAAC,cAAc;gBACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE;YAEnD,IAAI,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;gBAC7C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,aAAa,UAAU,YAAY,UAAU,cAAc,CAAC;gBACrF,MAAM,sBAAsB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;gBAC/E,MAAM,cAAc;oBAClB,GAAG,mBAAmB;oBACtB,IAAI,oBAAoB,EAAE,IAAI,aAAa,SAAS,SAAS,MAAM;oBACnE,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,OAAO,oBAAoB,KAAK,IAAI,aAAa,SAAS,SAAS,SAAS;oBAC5E,SAAS,KAAK,OAAO;gBACvB;gBACA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBACpD,qBAAqB;gBACrB,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;gBAEjC,IAAI,YAAY,QAAQ;oBACtB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ;gBACrC,OAAO,IAAI,UAAU;oBACnB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,eAAe,OAAO,OAAO;gBACnC,IAAI,aAAa,QAAQ,CAAC,UAAU,aAAa,QAAQ,CAAC,iBAAiB;oBACzE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,aAAa,UAAU,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB;gBAC9B;YACF;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,AAAC,CAAC,cAAc,CAAC,IAAI,WAAW,MAAM,GAAI,IAAI,CAAC,CAAC;4BAAC;;;;;;;;;;;kCAGxE,8OAAC;wBAAE,WAAU;;4BACV,KAAK,KAAK,CAAC,AAAC,CAAC,cAAc,CAAC,IAAI,WAAW,MAAM,GAAI;4BAAK;;;;;;;kCAG7D,8OAAC;wBAAI,WAAU;;;;;;oBAEd,+BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAM;gCACN,MAAK;gCACL,SAAQ;;kDAER,8OAAC;wCAAO,WAAU;wCAAa,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAK,QAAO;wCAAe,aAAY;;;;;;kDACxF,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,GAAE;;;;;;;;;;;;0CAGN,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,MAAM;wCACrB,MAAM,WAAW,gBAAgB;wCACjC,MAAM,cAAc,YAAY;wCAChC,MAAM,YAAY,iBAAiB;wCAEnC,qBACE,8OAAC;4CAEC,SAAS,IAAM,SAAS;4CACxB,UAAU,CAAC;4CACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA,WACI,0BACA,CAAC,YACD,qCACA;;8DAGN,8OAAC;8DAAM,KAAK,KAAK;;;;;;gDAChB,6BAAe,8OAAC,2NAAA,CAAA,cAAW;oDAAC,MAAM;oDAAI,WAAU;;;;;;;2CAb5C,KAAK,EAAE;;;;;oCAgBlB;;;;;;;;;;;0CAGJ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAE,GAAG,IAAI;kDACZ,cAAA,8OAAC;4CAAK,UAAU,KAAK,YAAY,CAAC;4CAAW,WAAU;;gDACpD,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAsB;;;;;;8EAC3C,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAEnB,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAoB,GAAG,KAAK;;;;;;;;;;;sGAEjD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAqB,GAAG,KAAK;;;;;;;;;;;sGAElD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8EAKpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAmB,GAAG,KAAK;;;;;;;;;;;sGAEhD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAuB,GAAG,KAAK;;;;;;;;;;;sGAEpD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8EAKpB,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8FACP,8OAAC,gIAAA,CAAA,YAAS;8FAAC;;;;;;8FACX,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wFAAC,aAAY;wFAAe,GAAG,KAAK;wFAAE,QAAQ;;;;;;;;;;;8FAEtD,8OAAC,gIAAA,CAAA,kBAAe;8FAAC;;;;;;8FACjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8EAIlB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAc,GAAG,KAAK;gGAAE,MAAK;;;;;;;;;;;sGAElD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAA0B,GAAG,KAAK;gGAAE,MAAK;;;;;;;;;;;sGAE9D,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8EAKpB,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;4EAAC,WAAU;;8FAClB,8OAAC,gIAAA,CAAA,YAAS;8FAAC;;;;;;8FACX,8OAAC,mIAAA,CAAA,UAAO;;sGACN,8OAAC,mIAAA,CAAA,iBAAc;4FAAC,OAAO;sGACrB,cAAA,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;oGACL,SAAQ;oGACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,CAAC,MAAM,KAAK,IAAI;;wGAGjB,MAAM,KAAK,IAAI,MAAM,KAAK,YAAY,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,OAAO,MACvE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,uBAEpB,8OAAC;sHAAK;;;;;;sHAER,8OAAC,0MAAA,CAAA,WAAY;4GAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sGAI9B,8OAAC,mIAAA,CAAA,iBAAc;4FAAC,WAAU;4FAAa,OAAM;sGAC3C,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gGACP,MAAK;gGACL,UAAU,MAAM,KAAK;gGACrB,UAAU,MAAM,QAAQ;gGACxB,UAAU,CAAC,OAAS,OAAO,IAAI,UAAU,OAAO,IAAI,KAAK;gGACzD,YAAY;;;;;;;;;;;;;;;;;8FAIlB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8EAIlB,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8FACP,8OAAC,gIAAA,CAAA,YAAS;8FAAC;;;;;;8FACX,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wFAAC,aAAY;wFAAgB,GAAG,KAAK;;;;;;;;;;;8FAE7C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8EAIlB,8OAAC,gIAAA,CAAA,YAAS;oEACR,SAAS,KAAK,OAAO;oEACrB,MAAK;oEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8FACP,8OAAC,gIAAA,CAAA,YAAS;8FAAC;;;;;;8FACX,8OAAC,gIAAA,CAAA,cAAW;8FACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wFAAC,aAAY;wFAAiB,GAAG,KAAK;;;;;;;;;;;8FAEjD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8EAIlB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAa,GAAG,KAAK;;;;;;;;;;;sGAE1C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAoB,GAAG,KAAK;;;;;;;;;;;sGAEjD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,kIAAA,CAAA,SAAM;4FAAC,eAAe,MAAM,QAAQ;4FAAE,OAAO,MAAM,KAAK;;8GACvD,8OAAC,gIAAA,CAAA,cAAW;8GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;kHACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4GAAC,aAAY;;;;;;;;;;;;;;;;8GAG7B,8OAAC,kIAAA,CAAA,gBAAa;;sHACZ,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAM;;;;;;sHACxB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAM;;;;;;sHACxB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;sHACvB,8OAAC,kIAAA,CAAA,aAAU;4GAAC,OAAM;sHAAK;;;;;;;;;;;;;;;;;;sGAG3B,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8EAKpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAqB,GAAG,KAAK;;;;;;;;;;;sGAElD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAuB,GAAG,KAAK;;;;;;;;;;;sGAEpD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8EAKpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAkB,GAAG,KAAK;;;;;;;;;;;sGAE/C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAe,GAAG,KAAK;;;;;;;;;;;sGAE5C,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sFAIlB,8OAAC,gIAAA,CAAA,YAAS;4EACR,SAAS,KAAK,OAAO;4EACrB,MAAK;4EACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sGACP,8OAAC,gIAAA,CAAA,YAAS;sGAAC;;;;;;sGACX,8OAAC,gIAAA,CAAA,cAAW;sGACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;gGAAC,aAAY;gGAAmB,GAAG,KAAK;;;;;;;;;;;sGAEhD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAQzB,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAsB;;;;;;8EAC3C,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAEnB,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;sEACrB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;kGAAC;;;;;;kGACX,8OAAC,kIAAA,CAAA,SAAM;wFAAC,eAAe,MAAM,QAAQ;wFAAE,OAAO,MAAM,KAAK;;0GACvD,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;8GACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wGAAC,aAAY;;;;;;;;;;;;;;;;0GAG7B,8OAAC,kIAAA,CAAA,gBAAa;;kHACZ,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAU;;;;;;kHAC5B,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAW;;;;;;;;;;;;;;;;;;kGAGjC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kFAIlB,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;kGAAC;;;;;;kGACX,8OAAC,kIAAA,CAAA,SAAM;wFAAC,eAAe,MAAM,QAAQ;wFAAE,OAAO,MAAM,KAAK;;0GACvD,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;8GACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wGAAC,aAAY;;;;;;;;;;;;;;;;0GAG7B,8OAAC,kIAAA,CAAA,gBAAa;0GACX,+BACC,8OAAC;oGAAI,WAAU;8GACb,cAAA,8OAAC;wGAAI,WAAU;;;;;;;;;;6GAEf,iBAAiB,MAAM,GAAG,IAC5B,iBAAiB,GAAG,CAAC,CAAC,uBACpB,8OAAC,kIAAA,CAAA,aAAU;wGAAiB,OAAO,OAAO,KAAK;kHAC5C,OAAO,KAAK;uGADE,OAAO,EAAE;;;;gIAK5B,8OAAC;oGAAI,WAAU;8GAAgC;;;;;;;;;;;;;;;;;kGAIrD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kFAIlB,8OAAC,gIAAA,CAAA,YAAS;wEACR,SAAS,KAAK,OAAO;wEACrB,MAAK;wEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kGACP,8OAAC,gIAAA,CAAA,YAAS;kGAAC;;;;;;kGACX,8OAAC,kIAAA,CAAA,SAAM;wFAAC,eAAe,MAAM,QAAQ;wFAAE,OAAO,MAAM,KAAK;;0GACvD,8OAAC,gIAAA,CAAA,cAAW;0GACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;8GACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wGAAC,aAAY;;;;;;;;;;;;;;;;0GAG7B,8OAAC,kIAAA,CAAA,gBAAa;;kHACZ,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAO;;;;;;kHACzB,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAS;;;;;;kHAC3B,8OAAC,kIAAA,CAAA,aAAU;wGAAC,OAAM;kHAAQ;;;;;;;;;;;;;;;;;;kGAG9B,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAQzB,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAsB;;;;;;8EAC3C,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAEnB,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,gIAAA,CAAA,YAAS;gEACR,SAAS,KAAK,OAAO;gEACrB,MAAK;gEACL,QAAQ,kBACN,8OAAC,gIAAA,CAAA,WAAQ;;0FACP,8OAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,8OAAC;;wFACE,6BACC,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC;gGAAE,WAAU;0GAAwB;;;;;;;;;;;wFAGxC,CAAC,gBAAgB,CAAC,uBACjB,8OAAC,kIAAA,CAAA,SAAM;4FACL,MAAK;4FACL,SAAS;4FACT,WAAU;;8GAEV,8OAAC,sMAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;gGAAiB;;;;;;;wFAItC,8BACC,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGACC,KAAK;oGACL,QAAQ;oGACR,WAAW;oGACX,WAAU;;;;;;8GAEZ,8OAAC;oGAAI,WAAU;;sHACb,8OAAC,kIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAS;4GACT,SAAQ;4GACR,WAAU;;8HAEV,8OAAC,oMAAA,CAAA,QAAK;oHAAC,WAAU;;;;;;gHAAiB;;;;;;;sHAGpC,8OAAC,kIAAA,CAAA,SAAM;4GACL,MAAK;4GACL,SAAS;4GACT,SAAQ;4GACR,WAAU;;8HAEV,8OAAC,4LAAA,CAAA,IAAC;oHAAC,WAAU;;;;;;gHAAiB;;;;;;;;;;;;;;;;;;;wFAMrC,CAAC,gBAAgB,CAAC,SAAS,aAAa,SAAS,KAAK,mBACrD,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGAAI,WAAU;8GACb,cAAA,8OAAC;wGAAI,WAAU;kHACZ,CAAC;4GACA,MAAM,eAAe,SAAS,aAAa,SAAS;4GACpD,IAAI,cAAc;gHAChB,qBACE,8OAAC,6HAAA,CAAA,UAAK;oHACJ,KACE,aAAa,UAAU,CAAC,WACpB,eACA,aAAa,UAAU,CAAC,UACxB,eACA,GAAG,8DAAwC,2BAA2B,aAAa,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;oHAEpH,KAAI;oHACJ,QAAQ;oHACR,OAAO;oHACP,WAAU;oHACV,OAAO;wHAAE,QAAQ;wHAAQ,OAAO;oHAAO;oHACvC,aAAa,aAAa,UAAU,CAAC;;;;;;4GAG3C;4GACA,qBACE,8OAAC;gHAAI,WAAU;0HACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oHAAC,WAAU;;;;;;;;;;;wGAGxB,CAAC;;;;;;;;;;;8GAGL,8OAAC,kIAAA,CAAA,SAAM;oGACL,MAAK;oGACL,SAAS;wGACP,SAAS;wGACT,eAAe;wGACf,SAAS,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;wGAC5B,KAAK,QAAQ,CAAC,SAAS;wGACvB;oGACF;oGACA,SAAQ;oGACR,WAAU;;sHAEV,8OAAC,sMAAA,CAAA,SAAM;4GAAC,WAAU;;;;;;wGAAiB;;;;;;;;;;;;;sGAKzC,8OAAC;4FAAO,KAAK;4FAAW,OAAO;gGAAE,SAAS;4FAAO;;;;;;;;;;;;;;;;;0FAGrD,8OAAC,gIAAA,CAAA,kBAAe;gFAAC,WAAU;0FAA6B;;;;;;0FAGxD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAOvB,gBAAgB,mBACf,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;;sEACd,8OAAC,gIAAA,CAAA,aAAU;;8EACT,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAsB;;;;;;8EAC3C,8OAAC,gIAAA,CAAA,kBAAe;8EAAC;;;;;;;;;;;;sEAInB,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,gIAAA,CAAA,YAAS;gEACR,SAAS,KAAK,OAAO;gEACrB,MAAK;gEACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;4EACN,CAAC,iCACA,8OAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;wFAAM,WAAU;;0GACf,8OAAC;gGAAI,WAAU;;kHACb,8OAAC,sMAAA,CAAA,SAAM;wGAAC,WAAU;;;;;;kHAClB,8OAAC;wGAAE,WAAU;;0HACX,8OAAC;gHAAK,WAAU;0HAAgB;;;;;;4GAAsB;;;;;;;kHAExD,8OAAC;wGAAE,WAAU;kHAAwB;;;;;;;;;;;;0GAEvC,8OAAC,iIAAA,CAAA,QAAK;gGACJ,IAAG;gGACH,MAAK;gGACL,QAAO;gGACP,WAAU;gGACV,UAAU,CAAC;oGACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oGAChC,IAAI,MAAM;wGACR,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;4GAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4GACZ;wGACF;wGACA,MAAM,kBAAkB;4GACtB,MAAM,KAAK,IAAI;4GACf,MAAM,KAAK,IAAI;4GACf,MAAM,KAAK,IAAI;4GACf,KAAK,IAAI,eAAe,CAAC;wGAC3B;wGACA,oBAAoB;wGACpB,qBAAqB;wGACrB,MAAM,QAAQ,CAAC;oGACjB;gGACF;;;;;;;;;;;;;;;;;;;;;uGAMR,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGAAI,WAAU;8GACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wGAAC,WAAU;;;;;;;;;;;8GAEtB,8OAAC;;sHACC,8OAAC;4GAAE,WAAU;sHAAqC,iBAAiB,IAAI;;;;;;sHACvE,8OAAC;4GAAE,WAAU;sHACV,4BAA4B,OACzB,eAAe,iBAAiB,IAAI,IACpC;;;;;;;;;;;;;;;;;;sGAIV,8OAAC;4FAAI,WAAU;;gGACZ,oBAAoB,SAAS,kCAC5B,8OAAC,kIAAA,CAAA,SAAM;oGACL,MAAK;oGACL,SAAQ;oGACR,MAAK;oGACL,SAAS,IAAM,OAAO,IAAI,CAAC,iBAAiB,GAAG,EAAE;oGACjD,WAAU;8GACX;;;;;;8GAIH,8OAAC,kIAAA,CAAA,SAAM;oGACL,MAAK;oGACL,SAAQ;oGACR,MAAK;oGACL,SAAS;oGACT,WAAU;8GAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0FAMvB,8OAAC,gIAAA,CAAA,kBAAe;gFAAC,WAAU;0FAA6B;;;;;;0FAGxD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;4DACxD,UAAU,gBAAgB;4DAC1B,WAAU;sEACX;;;;;;wDAGA,cAAc,WAAW,MAAM,GAAG,kBACjC,8OAAC;4DAAI,WAAU;;gEACZ,CAAC,YAAY,8BACZ,8OAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAI3C,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAS,IAAM,SAAS,cAAc;oEACtC,UAAU,CAAC,YAAY;oEACvB,WAAU;8EACX;;;;;;;;;;;iFAKH,8OAAC;4DAAI,WAAU;;gEACZ,CAAC,+BACA,8OAAC;oEAAE,WAAU;8EAA4B;;;;;;8EAI3C,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0FACA,iBAAiB,CAAC,eACd,0CACA;oEAEN,UAAU,gBAAgB,CAAC;8EAE1B,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc1D;AAEA,MAAM,qBAAqB;IACzB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QACP,wBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;;;;;;kBAMV,cAAA,8OAAC;;;;;;;;;;AAGP;uCAEe", "debugId": null}}]}