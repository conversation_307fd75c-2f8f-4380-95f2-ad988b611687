'use client';

import React, { useState, useRef, useEffect, Suspense } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, FileText, X, Camera, Upload, Check, CheckCircle, User, GraduationCap, Image as ImageIcon, FileCheck } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';
import { updateProfilePhoto } from '@/store/slices/studentProfileSlice';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import Header from '../../../app-components/Header';

const profileFormSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters.'),
  middleName: z.string().min(2, 'Middle name must be at least 2 characters.'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters.'),
  mothersName: z.string().min(2, 'Mother\'s name must be at least 2 characters.'),
  email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  contact: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.'),
  contactNo2: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.')
    .optional().or(z.literal('')),
  medium: z.string().min(1, 'Medium of instruction is required'),
  classroom: z.string().min(1, 'Standard is required'),
  gender: z.string().min(1, 'Gender is required'),
  birthday: z.date({ required_error: 'Please select your birthday' }),
  school: z.string().min(2, 'School name must be at least 2 characters.'),
  address: z.string().optional(),
  age: z.string().optional(),
  aadhaarNo: z.string().optional(),
  bloodGroup: z.string().optional(),
  birthPlace: z.string().optional(),
  motherTongue: z.string().optional(),
  religion: z.string().optional(),
  caste: z.string().optional(),
  subCaste: z.string().optional(),
  photo: z.any().optional(),
  document: z.any().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// Define the steps for the multistep form
const FORM_STEPS = [
  {
    id: 'personal',
    title: 'Personal Information',
    icon: User,
    description: 'Basic personal details'
  },
  {
    id: 'educational',
    title: 'Educational Information',
    icon: GraduationCap,
    description: 'Academic details'
  },
  {
    id: 'photo',
    title: 'Profile Photo',
    icon: ImageIcon,
    description: 'Upload your photo'
  },
  {
    id: 'document',
    title: 'Identity Document',
    icon: FileCheck,
    description: 'Verify your identity'
  }
];

const StudentProfileContent = () => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const searchParams = useSearchParams();
  const fromQuiz = searchParams.get('quiz') === 'true';
  const examId = searchParams.get('examId');

  const [currentStep, setCurrentStep] = useState(0);
  const [photo, setPhoto] = useState<string | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [uploadedDocument, setUploadedDocument] = useState<
    File | { name: string; size: number; url: string; type: string } | null
  >(null);
  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);

  const { profileData, loading: profileLoading } = useSelector(
    (state: RootState) => state.studentProfile
  );

  const profile = profileData?.profile || null;
  const classroomOptions = profileData?.classroomOptions || [];

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: '',
      middleName: '',
      lastName: '',
      mothersName: '',
      email: '',
      contact: '',
      contactNo2: '',
      medium: '',
      classroom: '',
      gender: '',
      birthday: undefined,
      school: '',
      address: '',
      age: '',
      aadhaarNo: '',
      bloodGroup: '',
      birthPlace: '',
      motherTongue: '',
      religion: '',
      caste: '',
      subCaste: '',
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    const studentToken = localStorage.getItem('studentToken');
    if (!studentToken) {
      toast.error('Please login to access your profile');
      router.push('/');
    }
  }, [router]);

  useEffect(() => {
    const studentToken = localStorage.getItem('studentToken');
    if (studentToken) {
      dispatch(fetchStudentProfile());
    }
  }, [dispatch]);

  useEffect(() => {
    if (isCameraOpen || !profileData) return;

    const profileObj = profileData.profile;
    const studentData = profileObj?.student || JSON.parse(localStorage.getItem('student_data') || '{}');

    const formValues = {
      firstName: studentData?.firstName || '',
      middleName: (profileObj as any)?.middleName || '',
      lastName: studentData?.lastName || '',
      mothersName: (profileObj as any)?.mothersName || '',
      email: studentData?.email || '',
      contact: studentData?.contact || '',
      contactNo2: (profileObj as any)?.contactNo2 || '',
      medium: profileObj?.medium || '',
      classroom: profileObj?.classroom || '',
      gender: (profileObj as any)?.gender || '',
      birthday: profileObj?.birthday ? new Date(profileObj.birthday) : undefined,
      school: profileObj?.school || '',
      address: profileObj?.address || '',
      age: (profileObj as any)?.age || '',
      aadhaarNo: (profileObj as any)?.aadhaarNo || '',
      bloodGroup: (profileObj as any)?.bloodGroup || '',
      birthPlace: (profileObj as any)?.birthPlace || '',
      motherTongue: (profileObj as any)?.motherTongue || '',
      religion: (profileObj as any)?.religion || '',
      caste: (profileObj as any)?.caste || '',
      subCaste: (profileObj as any)?.subCaste || '',
    };

    if (profileObj?.photo && !photo) {
      setPhoto(profileObj.photo);
      form.setValue('photo', profileObj.photo);
    }

    if (profileObj?.documentUrl && !uploadedDocument && !isDocumentRemoved) {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';
      const documentUrl = profileObj.documentUrl.startsWith('http')
        ? profileObj.documentUrl
        : `${baseUrl}${profileObj.documentUrl}`;

      const documentObj = {
        name: documentUrl.split('/').pop() || 'Uploaded Document',
        size: 0,
        url: documentUrl,
        type: 'application/octet-stream',
      };

      setUploadedDocument(documentObj);
      form.setValue('document', documentObj);
    }

    const currentValues = form.getValues();
    const isFormEmpty = !currentValues.firstName && !currentValues.lastName && !currentValues.contact;
    const isEducationalDataMissing = !currentValues.medium || !currentValues.classroom;

    if (isFormEmpty || isEducationalDataMissing) {
      form.reset(formValues);
    }
  }, [profileData, form, isCameraOpen, photo, uploadedDocument, isDocumentRemoved]);

  // Step validation functions
  const validatePersonalStep = () => {
    const values = form.getValues();
    return !!(
      values.firstName &&
      values.middleName &&
      values.lastName &&
      values.mothersName &&
      values.contact &&
      values.birthday &&
      values.school
    );
  };

  const validateEducationalStep = () => {
    const values = form.getValues();
    return !!(values.medium && values.classroom && values.gender);
  };

  const validatePhotoStep = () => {
    return !!(photo || profileData?.profile?.photo);
  };

  const validateDocumentStep = () => {
    return !!uploadedDocument && !isDocumentRemoved;
  };

  const isStepValid = (stepIndex: number) => {
    switch (stepIndex) {
      case 0: return validatePersonalStep();
      case 1: return validateEducationalStep();
      case 2: return validatePhotoStep();
      case 3: return validateDocumentStep();
      default: return false;
    }
  };

  const canProceedToStep = (stepIndex: number) => {
    if (stepIndex === 0) return true;
    for (let i = 0; i < stepIndex; i++) {
      if (!isStepValid(i)) return false;
    }
    return true;
  };

  const goToStep = (stepIndex: number) => {
    if (canProceedToStep(stepIndex)) {
      setCurrentStep(stepIndex);
    }
  };

  const isFormValid = React.useMemo(() => {
    const hasPhoto = !!(photo || profileData?.profile?.photo);
    const hasDocument = !!uploadedDocument && !isDocumentRemoved;

    return !!(
      form.getValues().firstName &&
      form.getValues().middleName &&
      form.getValues().lastName &&
      form.getValues().mothersName &&
      form.getValues().contact &&
      form.getValues().medium &&
      form.getValues().classroom &&
      form.getValues().gender &&
      form.getValues().birthday &&
      form.getValues().school &&
      hasPhoto &&
      hasDocument
    );
  }, [form, photo, uploadedDocument, profileData?.profile?.photo, isDocumentRemoved]);

  const onSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);

    try {
      const currentPhoto = photo || profileData?.profile?.photo;
      if (!currentPhoto) {
        toast.error('Please capture a photo for your profile');
        setIsSubmitting(false);
        return;
      }

      if (!uploadedDocument || isDocumentRemoved) {
        toast.error('Identity document is required. Please upload a document.');
        setIsSubmitting(false);
        return;
      }

      if (!(await form.trigger())) {
        toast.error('Please fill in all required fields correctly');
        setIsSubmitting(false);
        return;
      }

      const jsonData: any = {
        firstName: data.firstName,
        middleName: data.middleName,
        lastName: data.lastName,
        mothersName: data.mothersName,
        email: data.email,
        contact: data.contact,
        contactNo2: data.contactNo2,
        medium: data.medium,
        classroom: data.classroom,
        gender: data.gender,
        birthday: data.birthday?.toISOString() || '',
        school: data.school,
        address: data.address,
        age: data.age,
        aadhaarNo: data.aadhaarNo,
        bloodGroup: data.bloodGroup,
        birthPlace: data.birthPlace,
        motherTongue: data.motherTongue,
        religion: data.religion,
        caste: data.caste,
        subCaste: data.subCaste,
      };

      const studentToken = localStorage.getItem('studentToken');
      if (!studentToken) {
        toast.error('Please login to submit your profile');
        router.push('/');
        return;
      }

      const result = await dispatch(updateStudentProfile(jsonData));

      if (result.meta.requestStatus === 'fulfilled') {
        toast.success(`Profile ${profile ? 'updated' : 'created'} successfully!`);

        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');
        const studentData = {
          ...existingStudentData,
          id: existingStudentData.id || profileData?.profile?.student?.id || '',
          firstName: data.firstName,
          lastName: data.lastName,
          email: existingStudentData.email || profileData?.profile?.student?.email || '',
          contact: data.contact,
        };

        localStorage.setItem('student_data', JSON.stringify(studentData));
        setIsDocumentRemoved(false);
        await dispatch(fetchStudentProfile());

        if (fromQuiz) {
          if (examId) {
            router.push(`/uwhiz-exam/${examId}`);
          } else {
            router.push('/mock-test');
          }
        } else {
          router.push('/');
        }
      } else if (result.meta.requestStatus === 'rejected') {
        const errorMessage = result.payload as string;

        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
          toast.error('Your session has expired. Please login again.');
          localStorage.removeItem('studentToken');
          router.push('/');
        } else {
          toast.error(errorMessage || 'Failed to update profile');
        }
      }
    } catch {
      toast.error('Failed to submit profile information');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Header />
      <div className="space-y-6 p-10 pb-4 md:block">
        <div className="space-y-0.5">
          <h2 className="text-2xl font-bold tracking-tight">
            Student Profile
          </h2>
          <p className="text-muted-foreground">
            Complete your profile information. Your progress will be automatically saved as you complete each section.
          </p>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-black h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${((currentStep + 1) / FORM_STEPS.length) * 100}%` }}
          />
        </div>
        <p className="text-sm text-muted-foreground">
          {Math.round(((currentStep + 1) / FORM_STEPS.length) * 100)}% complete
        </p>

        <div className="my-6 border-t border-gray-200" />

        {profileLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <svg
              className="animate-spin h-10 w-10 text-black mb-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            <p className="text-gray-600">Loading profile information...</p>
          </div>
        ) : (
          <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
            <aside className="-mx-4 lg:w-1/6 pb-12">
              <nav className="space-y-1">
                {FORM_STEPS.map((step, index) => {
                  const isActive = currentStep === index;
                  const isCompleted = isStepValid(index);
                  const canAccess = canProceedToStep(index);

                  return (
                    <button
                      key={step.id}
                      onClick={() => canAccess && goToStep(index)}
                      disabled={!canAccess}
                      className={cn(
                        "flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors",
                        isActive
                          ? 'bg-muted text-primary'
                          : !canAccess
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-muted-foreground hover:text-primary'
                      )}
                    >
                      <span>{step.title}</span>
                      {isCompleted && <CheckCircle size={16} className="text-green-500" />}
                    </button>
                  );
                })}
              </nav>
            </aside>
            <div className="flex justify-center w-full">
              <div className="flex-1 lg:max-w-2xl pb-12">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* Step Content */}
                    {currentStep === 0 && (
                      <Card className="shadow-sm">
                        <CardHeader>
                          <CardTitle className="text-lg font-medium">Personal Information</CardTitle>
                          <CardDescription>Basic personal details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="firstName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>First Name *</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter First Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="middleName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Middle Name *</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Middle Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="lastName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Last Name *</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Last Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="mothersName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Mother's Name *</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Mother's Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Navigation Buttons */}
                    <div className="flex flex-col sm:flex-row justify-between gap-4 pt-6">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
                        disabled={currentStep === 0}
                        className="px-6 order-2 sm:order-1"
                      >
                        Previous
                      </Button>

                      {currentStep < FORM_STEPS.length - 1 ? (
                        <div className="flex flex-col items-end order-1 sm:order-2">
                          {!isStepValid(currentStep) && (
                            <p className="text-xs text-red-500 mb-2">
                              Please complete all required fields
                            </p>
                          )}
                          <Button
                            type="button"
                            onClick={() => {
                              if (isStepValid(currentStep)) {
                                setCurrentStep(currentStep + 1);
                              } else {
                                toast.error('Please complete all required fields in this step');
                              }
                            }}
                            disabled={!isStepValid(currentStep)}
                            className="px-6 bg-black text-white hover:bg-gray-800 disabled:bg-gray-300"
                          >
                            Next
                          </Button>
                        </div>
                      ) : (
                        <div className="flex flex-col items-end order-1 sm:order-2">
                          {!isFormValid && (
                            <p className="text-xs text-red-500 mb-2">
                              Please complete all steps to save your profile
                            </p>
                          )}
                          <Button
                            type="submit"
                            className={`px-6 font-medium py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 ${
                              isFormValid && !isSubmitting
                                ? 'bg-black text-white hover:bg-gray-800'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            }`}
                            disabled={isSubmitting || !isFormValid}
                          >
                            {isSubmitting ? 'Saving...' : 'Save Profile'}
                          </Button>
                        </div>
                      )}
                    </div>
                  </form>
                </Form>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

const StudentProfilePage = () => {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <svg
            className="animate-spin h-10 w-10 text-black"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </div>
      }
    >
      <StudentProfileContent />
    </Suspense>
  );
};

export default StudentProfilePage;
