'use client';

import React, { useState, useRef, useEffect, useCallback, Suspense } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, FileText, X, Camera, Upload, Check, CheckCircle, User, GraduationCap, Image as ImageIcon, FileCheck } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';
import { updateProfilePhoto } from '@/store/slices/studentProfileSlice';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import Header from '../../../app-components/Header';

// Enhanced schema with stricter validation for photo and document
const profileFormSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters.'),
  middleName: z.string().optional(),
  lastName: z.string().min(2, 'Last name must be at least 2 characters.'),
  mothersName: z.string().optional(),
  email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  contact: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.'),
  contactNo2: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.')
    .optional().or(z.literal('')),
  medium: z.string().min(1, 'Medium of instruction is required'),
  classroom: z.string().min(1, 'Standard is required'),
  gender: z.string().min(1, 'Gender is required'),
  birthday: z.date({ required_error: 'Please select your birthday' }),
  school: z.string().min(2, 'School name must be at least 2 characters.'),
  address: z.string().optional(),
  age: z.string().optional(),
  aadhaarNo: z.string().optional(),
  bloodGroup: z.string().optional(),
  birthPlace: z.string().optional(),
  motherTongue: z.string().optional(),
  religion: z.string().optional(),
  caste: z.string().optional(),
  subCaste: z.string().optional(),
  photo: z.string().optional(), // Base64 string or URL
  document: z.instanceof(File).optional().refine(
    (file) => !file || file.size <= 5 * 1024 * 1024,
    'Document size must not exceed 5MB'
  ),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

interface DocumentWithUrl {
  name: string;
  size: number;
  url: string;
  type: string;
}

const FORM_STEPS = [
  { id: 'personal', title: 'Personal Information', icon: User, description: 'Basic personal details' },
  { id: 'educational', title: 'Educational Information', icon: GraduationCap, description: 'Academic details' },
  { id: 'photo', title: 'Profile Photo', icon: ImageIcon, description: 'Upload your photo' },
  { id: 'document', title: 'Identity Document', icon: FileCheck, description: 'Verify your identity' },
];

const StudentProfileContent = () => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const searchParams = useSearchParams();
  const fromQuiz = searchParams.get('quiz') === 'true';
  const examId = searchParams.get('examId');

  const [currentStep, setCurrentStep] = useState(0);
  const [photo, setPhoto] = useState<string | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [uploadedDocument, setUploadedDocument] = useState<File | DocumentWithUrl | null>(null);
  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);

  const { profileData, loading: profileLoading } = useSelector(
    (state: RootState) => state.studentProfile
  );
  const classroomOptions = profileData?.classroomOptions || [];

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: '',
      middleName: '',
      lastName: '',
      mothersName: '',
      email: '',
      contact: '',
      contactNo2: '',
      medium: '',
      classroom: '',
      gender: '',
      birthday: undefined,
      school: '',
      address: '',
      age: '',
      aadhaarNo: '',
      bloodGroup: '',
      birthPlace: '',
      motherTongue: '',
      religion: '',
      caste: '',
      subCaste: '',
      photo: '',
      document: undefined,
    },
    mode: 'onSubmit',
  });

  // Authentication check
  useEffect(() => {
    const studentToken = localStorage.getItem('studentToken');
    if (!studentToken) {
      toast.error('Please login to access your profile');
      router.push('/');
    } else {
      dispatch(fetchStudentProfile());
    }
  }, [dispatch, router]);

  // Populate form with profile data
  useEffect(() => {
    if (profileLoading || isCameraOpen || !profileData?.profile) return;

    const profile = profileData.profile;
    const studentData = profile.student || JSON.parse(localStorage.getItem('student_data') || '{}');

    const formValues: ProfileFormValues = {
      firstName: studentData.firstName || '',
      middleName: studentData.middleName || '',
      lastName: studentData.lastName || '',
      mothersName: studentData.mothersName || '',
      email: studentData.email || '',
      contact: studentData.contact || '',
      contactNo2: studentData.contactNo2 || '',
      medium: profile.medium || '',
      classroom: profile.classroom || '',
      gender: studentData.gender || '',
      birthday: studentData.birthday ? new Date(studentData.birthday) : new Date('2000-01-01'),
      school: profile.school || '',
      address: profile.address || '',
      age: studentData.age || '',
      aadhaarNo: studentData.aadhaarNo || '',
      bloodGroup: studentData.bloodGroup || '',
      birthPlace: studentData.birthPlace || '',
      motherTongue: studentData.motherTongue || '',
      religion: studentData.religion || '',
      caste: studentData.caste || '',
      subCaste: studentData.subCaste || '',
      photo: profile.photo || '',
      document: undefined,
    };

    if (profile.photo && !photo) {
      setPhoto(profile.photo);
      form.setValue('photo', profile.photo);
    }

    if (profile.documentUrl && !uploadedDocument && !isDocumentRemoved) {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';
      const documentUrl = profile.documentUrl.startsWith('http')
        ? profile.documentUrl
        : `${baseUrl}${profile.documentUrl}`;
      setUploadedDocument({
        name: documentUrl.split('/').pop() || 'Uploaded Document',
        size: 0,
        url: documentUrl,
        type: 'application/octet-stream',
      });
      form.setValue('document', undefined);
    }

    form.reset(formValues);
  }, [profileData, profileLoading, isCameraOpen, photo, uploadedDocument, isDocumentRemoved, form]);

  // Step validation
  const validatePersonalStep = useCallback(() => {
    const values = form.getValues();
    return !!(
      values.firstName &&
      values.lastName &&
      values.contact &&
      values.birthday &&
      values.school
    );
  }, [form]);

  const validateEducationalStep = useCallback(() => {
    const values = form.getValues();
    return !!(values.medium && values.classroom && values.gender);
  }, [form]);

  const validatePhotoStep = useCallback(() => {
    return !!(photo || profileData?.profile?.photo);
  }, [photo, profileData]);

  const validateDocumentStep = useCallback(() => {
    return !!uploadedDocument && !isDocumentRemoved;
  }, [uploadedDocument, isDocumentRemoved]);

  const isStepValid = useCallback(
    (stepIndex: number) => {
      switch (stepIndex) {
        case 0:
          return validatePersonalStep();
        case 1:
          return validateEducationalStep();
        case 2:
          return validatePhotoStep();
        case 3:
          return validateDocumentStep();
        default:
          return false;
      }
    },
    [validatePersonalStep, validateEducationalStep, validatePhotoStep, validateDocumentStep]
  );

  const canProceedToStep = useCallback(
    (stepIndex: number) => {
      if (stepIndex === 0) return true;
      for (let i = 0; i < stepIndex; i++) {
        if (!isStepValid(i)) return false;
      }
      return true;
    },
    [isStepValid]
  );

  const goToStep = useCallback(
    (stepIndex: number) => {
      if (canProceedToStep(stepIndex)) {
        setCurrentStep(stepIndex);
      } else {
        toast.error('Please complete all required fields in the previous steps.');
      }
    },
    [canProceedToStep]
  );

  const openCamera = useCallback(async () => {
    setCameraError(null);
    try {
      if (!navigator.mediaDevices?.getUserMedia) {
        throw new Error('Camera not supported on this device');
      }
      setIsCameraOpen(true);
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' },
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play().catch(() => toast.error('Error starting camera preview'));
        };
      }
    } catch (error: any) {
      setIsCameraOpen(false);
      const message =
        error.name === 'NotAllowedError'
          ? 'Please allow camera access in your browser settings.'
          : 'Could not access camera. Please check your camera settings.';
      setCameraError(message);
      toast.error(message);
    }
  }, []);

  const compressImage = useCallback((canvas: HTMLCanvasElement, maxWidth: number = 800, quality: number = 0.6): string => {
    const context = canvas.getContext('2d');
    if (!context) return '';

    const originalWidth = canvas.width;
    const originalHeight = canvas.height;
    let newWidth = originalWidth;
    let newHeight = originalHeight;

    if (originalWidth > maxWidth) {
      newWidth = maxWidth;
      newHeight = (originalHeight * maxWidth) / originalWidth;
    }

    const compressedCanvas = document.createElement('canvas');
    compressedCanvas.width = newWidth;
    compressedCanvas.height = newHeight;

    const compressedContext = compressedCanvas.getContext('2d');
    if (!compressedContext) return '';

    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);
    return compressedCanvas.toDataURL('image/jpeg', quality);
  }, []);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    context?.clearRect(0, 0, canvas.width, canvas.height);
    context?.save();
    context?.scale(-1, 1);
    context?.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);
    context?.restore();

    const compressedPhotoDataUrl = compressImage(canvas);
    const base64Data = compressedPhotoDataUrl.split(',')[1];
    const sizeInKB = (base64Data.length * 3) / 4 / 1024;

    if (sizeInKB > 5120) {
      toast.error('Photo size exceeds 5MB limit. Please try again.');
      return;
    }

    setPhoto(compressedPhotoDataUrl);
    form.setValue('photo', compressedPhotoDataUrl);
    dispatch(updateProfilePhoto(compressedPhotoDataUrl));
    closeCamera();
  }, [compressImage, dispatch, form]);

  const closeCamera = useCallback(() => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsCameraOpen(false);
    setCameraError(null);
  }, []);

  const removeDocument = useCallback(() => {
    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {
      URL.revokeObjectURL(uploadedDocument.url);
    }
    setUploadedDocument(null);
    setIsDocumentRemoved(true);
    form.setValue('document', undefined);
  }, [uploadedDocument, form]);

  const formatFileSize = useCallback((bytes: number) => {
    if (bytes < 1024) return `${bytes} bytes`;
    else if (bytes < 1048576) return `${(bytes / 1024).toFixed(1)} KB`;
    else return `${(bytes / 1048576).toFixed(1)} MB`;
  }, []);

  const isFormValid = useCallback(() => {
    return !!(
      validatePersonalStep() &&
      validateEducationalStep() &&
      validatePhotoStep() &&
      validateDocumentStep()
    );
  }, [validatePersonalStep, validateEducationalStep, validatePhotoStep, validateDocumentStep]);

  const onSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);
    try {
      if (!validatePhotoStep()) {
        toast.error('Please capture or upload a profile photo.');
        setIsSubmitting(false);
        return;
      }

      if (!validateDocumentStep()) {
        toast.error('Please upload an identity document.');
        setIsSubmitting(false);
        return;
      }

      if (!(await form.trigger())) {
        toast.error('Please fill in all required fields correctly.');
        setIsSubmitting(false);
        return;
      }

      const jsonData: any = {
        ...data,
        birthday: data.birthday?.toISOString() || '',
      };

      if (data.photo?.startsWith('data:')) {
        const base64Data = data.photo.split(',')[1];
        jsonData.photo = base64Data;
        jsonData.photoMimeType = 'image/jpeg';
      }

      if (data.document instanceof File) {
        const documentBase64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve((reader.result as string).split(',')[1]);
          reader.onerror = reject;
          if (data.document) {
            reader.readAsDataURL(data.document);
          } else {
            reject(new Error('No document file provided'));
          }
        });
        jsonData.document = documentBase64;
        jsonData.documentMimeType = data.document.type;
        jsonData.documentName = data.document.name;
      }

      if (isDocumentRemoved && profileData?.profile?.documentUrl) {
        jsonData.removeDocument = true;
      }

      const studentToken = localStorage.getItem('studentToken');
      if (!studentToken) {
        toast.error('Please login to submit your profile');
        router.push('/');
        return;
      }

      const result = await dispatch(updateStudentProfile(jsonData));

      if (result.meta.requestStatus === 'fulfilled') {
        toast.success(`Profile ${profileData?.profile ? 'updated' : 'created'} successfully!`);
        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');
        const studentData = {
          ...existingStudentData,
          id: existingStudentData.id || profileData?.profile?.student?.id || '',
          firstName: data.firstName,
          lastName: data.lastName,
          email: existingStudentData.email || profileData?.profile?.student?.email || '',
          contact: data.contact,
        };
        localStorage.setItem('student_data', JSON.stringify(studentData));
        setIsDocumentRemoved(false);
        await dispatch(fetchStudentProfile());

        if (fromQuiz && examId) {
          router.push(`/uwhiz-exam/${examId}`);
        } else if (fromQuiz) {
          router.push('/mock-test');
        } else {
          router.push('/');
        }
      } else {
        const errorMessage = result.payload as string;
        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
          toast.error('Your session has expired. Please login again.');
          localStorage.removeItem('studentToken');
          router.push('/');
        } else {
          toast.error(errorMessage || 'Failed to update profile');
        }
      }
    } catch (error) {
      toast.error('Failed to submit profile information');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Header />
      <div className="space-y-6 p-10 pb-4 md:block">
        <div className="space-y-0.5">
          <h2 className="text-2xl font-bold tracking-tight">Student Profile</h2>
          <p className="text-muted-foreground">
            Complete your profile information. Your progress will be automatically saved as you complete each section.
          </p>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-black h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${((currentStep + 1) / FORM_STEPS.length) * 100}%` }}
          />
        </div>
        <p className="text-sm text-muted-foreground">
          {Math.round(((currentStep + 1) / FORM_STEPS.length) * 100)}% complete
        </p>

        <div className="my-6 border-t border-gray-200" />

        {profileLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <svg
              className="animate-spin h-10 w-10 text-black mb-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            <p className="text-gray-600">Loading profile information...</p>
          </div>
        ) : (
          <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
            <aside className="-mx-4 lg:w-1/6 pb-12">
              <nav className="space-y-1">
                {FORM_STEPS.map((step, index) => {
                  const isActive = currentStep === index;
                  const isCompleted = isStepValid(index);
                  const canAccess = canProceedToStep(index);

                  return (
                    <button
                      key={step.id}
                      onClick={() => goToStep(index)}
                      disabled={!canAccess}
                      className={cn(
                        'flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors',
                        isActive
                          ? 'bg-muted text-primary'
                          : !canAccess
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-muted-foreground hover:text-primary'
                      )}
                    >
                      <span>{step.title}</span>
                      {isCompleted && <CheckCircle size={16} className="text-green-500" />}
                    </button>
                  );
                })}
              </nav>
            </aside>
            <div className="flex justify-center w-full">
              <div className="flex-1 lg:max-w-2xl pb-12">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {currentStep === 0 && (
                      <Card className="shadow-sm">
                        <CardHeader>
                          <CardTitle className="text-lg font-medium">Personal Information</CardTitle>
                          <CardDescription>Basic personal details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="firstName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>First Name *</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter First Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="middleName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Middle Name</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Middle Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="lastName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Last Name *</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Last Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="mothersName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Mother's Name</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Mother's Name" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email</FormLabel>
                                <FormControl>
                                  <Input placeholder="Enter Email" {...field} disabled />
                                </FormControl>
                                <FormDescription>Email cannot be changed</FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="contact"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Contact Number *</FormLabel>
                                  <FormControl>
                                    <Input placeholder="8520369851" {...field} type="tel" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="contactNo2"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Contact Number 2</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Alternate Number" {...field} type="tel" />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <FormField
                            control={form.control}
                            name="birthday"
                            render={({ field }) => (
                              <FormItem className="flex flex-col">
                                <FormLabel>Date of Birth *</FormLabel>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <FormControl>
                                      <Button
                                        variant="outline"
                                        className={cn(
                                          'w-full pl-3 text-left font-normal',
                                          !field.value && 'text-muted-foreground'
                                        )}
                                      >
                                        {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (
                                          format(field.value, 'PPP')
                                        ) : (
                                          <span>Select your birthday</span>
                                        )}
                                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                      </Button>
                                    </FormControl>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                      mode="single"
                                      selected={field.value}
                                      onSelect={field.onChange}
                                      disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="school"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>School Name *</FormLabel>
                                <FormControl>
                                  <Input placeholder="Enter School" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="address"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Address</FormLabel>
                                <FormControl>
                                  <Textarea placeholder="Enter Address" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <FormField
                              control={form.control}
                              name="age"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Age</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Age" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="aadhaarNo"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Aadhaar Number</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Aadhaar No" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="bloodGroup"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Blood Group</FormLabel>
                                  <Select onValueChange={field.onChange} value={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="A+">A+</SelectItem>
                                      <SelectItem value="A-">A-</SelectItem>
                                      <SelectItem value="B+">B+</SelectItem>
                                      <SelectItem value="B-">B-</SelectItem>
                                      <SelectItem value="AB+">AB+</SelectItem>
                                      <SelectItem value="AB-">AB-</SelectItem>
                                      <SelectItem value="O+">O+</SelectItem>
                                      <SelectItem value="O-">O-</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="birthPlace"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Birth Place</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Birth Place" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="motherTongue"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Mother Tongue</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Mother Tongue" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <FormField
                              control={form.control}
                              name="religion"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Religion</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Religion" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="caste"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Caste</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Caste" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="subCaste"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Sub Caste</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Enter Sub Caste" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    )}
                    {currentStep === 1 && (
                      <Card className="shadow-sm">
                        <CardHeader>
                          <CardTitle className="text-lg font-medium">Educational Information</CardTitle>
                          <CardDescription>Academic details</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <FormField
                              control={form.control}
                              name="medium"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Medium *</FormLabel>
                                  <Select onValueChange={field.onChange} value={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="english">English</SelectItem>
                                      <SelectItem value="gujarati">Gujarati</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="classroom"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Standard *</FormLabel>
                                  <Select onValueChange={field.onChange} value={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {profileLoading ? (
                                        <div className="flex items-center justify-center p-4">
                                          <div className="animate-spin h-5 w-5 border-2 border-gray-300 border-t-black rounded-full" />
                                        </div>
                                      ) : classroomOptions.length > 0 ? (
                                        classroomOptions.map((option) => (
                                          <SelectItem key={option.id} value={option.value}>
                                            {option.value}
                                          </SelectItem>
                                        ))
                                      ) : (
                                        <div className="p-2 text-center text-gray-500">No classroom options available</div>
                                      )}
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="gender"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Gender *</FormLabel>
                                  <Select onValueChange={field.onChange} value={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="male">Male</SelectItem>
                                      <SelectItem value="female">Female</SelectItem>
                                      <SelectItem value="other">Other</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    )}
                    {currentStep === 2 && (
                      <Card className="shadow-sm">
                        <CardHeader>
                          <CardTitle className="text-lg font-medium">Profile Photo</CardTitle>
                          <CardDescription>Take a clear photo of your face for your profile (MAX. 5MB)</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <FormField
                            control={form.control}
                            name="photo"
                            render={() => (
                              <FormItem>
                                <FormControl>
                                  <div>
                                    {cameraError && (
                                      <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                        <p className="text-red-700 text-sm">{cameraError}</p>
                                      </div>
                                    )}
                                    {!isCameraOpen && !photo && (
                                      <Button
                                        type="button"
                                        onClick={openCamera}
                                        className="w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2"
                                      >
                                        <Camera className="h-5 w-5 mr-2" />
                                        Open Camera
                                      </Button>
                                    )}
                                    {isCameraOpen && (
                                      <div className="camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                                        <video
                                          ref={videoRef}
                                          autoPlay
                                          playsInline
                                          className="w-full h-auto transform scale-x-[-1]"
                                        />
                                        <div className="flex p-4 bg-gray-50">
                                          <Button
                                            type="button"
                                            onClick={capturePhoto}
                                            variant="default"
                                            className="flex-1 mr-2 bg-black hover:bg-gray-800 text-white"
                                          >
                                            <Check className="h-4 w-4 mr-2" />
                                            Capture
                                          </Button>
                                          <Button
                                            type="button"
                                            onClick={closeCamera}
                                            variant="outline"
                                            className="flex-1 border-gray-300"
                                          >
                                            <X className="h-4 w-4 mr-2" />
                                            Cancel
                                          </Button>
                                        </div>
                                      </div>
                                    )}
                                    {!isCameraOpen && (photo || profileData?.profile?.photo) && (
                                      <div className="flex flex-col sm:flex-row items-center gap-4">
                                        <div className="border rounded-lg shadow-md bg-gray-50 p-4 max-w-full">
                                          <div className="flex justify-center">
                                            {(() => {
                                              const displayPhoto = photo || profileData?.profile?.photo;
                                              if (displayPhoto) {
                                                return (
                                                  <Image
                                                    src={
                                                      displayPhoto.startsWith('data:')
                                                        ? displayPhoto
                                                        : displayPhoto.startsWith('http')
                                                        ? displayPhoto
                                                        : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`
                                                    }
                                                    alt="Student Photo"
                                                    height={1000}
                                                    width={1000}
                                                    className="max-w-full max-h-80 object-contain rounded-lg"
                                                    style={{ height: 'auto', width: 'auto' }}
                                                    unoptimized={displayPhoto.startsWith('data:')}
                                                  />
                                                );
                                              }
                                              return (
                                                <div className="flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg">
                                                  <Camera className="h-12 w-12 text-gray-400" />
                                                </div>
                                              );
                                            })()}
                                          </div>
                                        </div>
                                        <Button
                                          type="button"
                                          onClick={() => {
                                            setPhoto(null);
                                            setCameraError(null);
                                            dispatch(updateProfilePhoto(undefined));
                                            form.setValue('photo', '');
                                            openCamera();
                                          }}
                                          variant="outline"
                                          className="border-gray-300"
                                        >
                                          <Camera className="h-4 w-4 mr-2" />
                                          Retake Photo
                                        </Button>
                                      </div>
                                    )}
                                    <canvas ref={canvasRef} style={{ display: 'none' }} />
                                  </div>
                                </FormControl>
                                <FormDescription className="text-xs text-gray-500 mt-2">
                                  A clear photo helps us identify you and personalize your profile
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    )}
                    {currentStep === 3 && (
                      <Card className="shadow-sm">
                        <CardHeader>
                          <CardTitle className="text-lg font-medium">Identity Document</CardTitle>
                          <CardDescription>
                            Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <FormField
                            control={form.control}
                            name="document"
                            render={({ field }) => (
                              <FormItem>
                                {!uploadedDocument ? (
                                  <FormControl>
                                    <div className="flex items-center justify-center w-full">
                                      <label className="flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                          <Upload className="w-10 h-10 mb-3 text-black" />
                                          <p className="mb-2 text-sm text-gray-700">
                                            <span className="font-semibold">Click to upload</span> or drag and drop
                                          </p>
                                          <p className="text-xs text-gray-500">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>
                                        </div>
                                        <Input
                                          id="document"
                                          type="file"
                                          accept=".pdf,.jpg,.jpeg,.png"
                                          className="hidden"
                                          onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) {
                                              if (file.size > 5 * 1024 * 1024) {
                                                toast.error('File size exceeds 5MB limit');
                                                return;
                                              }
                                              const documentWithUrl = {
                                                name: file.name,
                                                size: file.size,
                                                type: file.type,
                                                url: URL.createObjectURL(file),
                                              };
                                              setUploadedDocument(documentWithUrl);
                                              setIsDocumentRemoved(false);
                                              field.onChange(file);
                                            }
                                          }}
                                        />
                                      </label>
                                    </div>
                                  </FormControl>
                                ) : (
                                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-3">
                                        <div className="p-2 bg-[#fff8f3] rounded-full">
                                          <FileText className="h-5 w-5 text-black" />
                                        </div>
                                        <div>
                                          <p className="text-sm font-medium text-gray-700">{uploadedDocument.name}</p>
                                          <p className="text-xs text-gray-500">
                                            {uploadedDocument instanceof File
                                              ? formatFileSize(uploadedDocument.size)
                                              : 'Previously uploaded document'}
                                          </p>
                                        </div>
                                      </div>
                                      <div className="flex space-x-2">
                                        {uploadedDocument && 'url' in uploadedDocument && (
                                          <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => window.open(uploadedDocument.url, '_blank')}
                                            className="h-8 px-3 border-gray-200"
                                          >
                                            View
                                          </Button>
                                        )}
                                        <Button
                                          type="button"
                                          variant="outline"
                                          size="sm"
                                          onClick={removeDocument}
                                          className="h-8 w-8 p-0 border-gray-200"
                                        >
                                          <X className="h-4 w-4 text-gray-500" />
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                <FormDescription className="text-xs text-gray-500 mt-2">
                                  This document will serve to verify your identity and date of birth.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </CardContent>
                      </Card>
                    )}
                    <div className="flex flex-col sm:flex-row justify-between gap-4 pt-6">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
                        disabled={currentStep === 0}
                        className="px-6 order-2 sm:order-1"
                      >
                        Previous
                      </Button>
                      {currentStep < FORM_STEPS.length - 1 ? (
                        <div className="flex flex-col items-end order-1 sm:order-2">
                          {!isStepValid(currentStep) && (
                            <p className="text-xs text-red-500 mb-2">
                              Please complete all required fields
                            </p>
                          )}
                          <Button
                            type="button"
                            onClick={() => goToStep(currentStep + 1)}
                            disabled={!isStepValid(currentStep)}
                            className="px-6 bg-black text-white hover:bg-gray-800 disabled:bg-gray-300"
                          >
                            Next
                          </Button>
                        </div>
                      ) : (
                        <div className="flex flex-col items-end order-1 sm:order-2">
                          {!isFormValid() && (
                            <p className="text-xs text-red-500 mb-2">
                              Please complete all steps to save your profile
                            </p>
                          )}
                          <Button
                            type="submit"
                            className={cn(
                              'px-6 font-medium py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200',
                              isFormValid() && !isSubmitting
                                ? 'bg-black text-white hover:bg-gray-800'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            )}
                            disabled={isSubmitting || !isFormValid()}
                          >
                            {isSubmitting ? 'Saving...' : 'Save Profile'}
                          </Button>
                        </div>
                      )}
                    </div>
                  </form>
                </Form>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

const StudentProfilePage = () => {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <svg
            className="animate-spin h-10 w-10 text-black"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </div>
      }
    >
      <StudentProfileContent />
    </Suspense>
  );
};

export default StudentProfilePage;